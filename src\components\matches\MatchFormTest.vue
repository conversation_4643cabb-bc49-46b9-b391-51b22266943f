<template>
  <div class="p-8 max-w-4xl mx-auto">
    <h1 class="text-2xl font-bold mb-6">Match Form Test</h1>
    
    <div class="space-y-4 mb-8">
      <h2 class="text-lg font-semibold">Test Cases:</h2>
      <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
        <button 
          @click="testAutoEndDate" 
          class="p-3 bg-blue-500 text-white rounded hover:bg-blue-600"
        >
          Test Auto End Date
        </button>
        <button 
          @click="testPostcodeLookup" 
          class="p-3 bg-green-500 text-white rounded hover:bg-green-600"
        >
          Test Postcode Lookup
        </button>
        <button 
          @click="testMapUpdate" 
          class="p-3 bg-purple-500 text-white rounded hover:bg-purple-600"
        >
          Test Map Update
        </button>
      </div>
    </div>

    <div class="border rounded-lg p-4">
      <MatchForm 
        :initial-data="testData"
        @submit="onSubmit"
        @cancel="onCancel"
      />
    </div>

    <div v-if="lastSubmittedData" class="mt-8 p-4 bg-gray-100 rounded">
      <h3 class="font-semibold mb-2">Last Submitted Data:</h3>
      <pre class="text-sm overflow-auto">{{ JSON.stringify(lastSubmittedData, null, 2) }}</pre>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import MatchForm from './MatchForm.vue'
import type { MatchFormData } from '@/stores/matches'

const testData = ref<Partial<MatchFormData>>({})
const lastSubmittedData = ref<MatchFormData | null>(null)

function testAutoEndDate() {
  testData.value = {
    name: 'Test Match',
    description: 'Testing auto end date functionality',
    startDate: '2024-08-15T09:00',
    // endDate intentionally left empty to test auto-setting
    country: 'Poland',
    city: 'Warsaw'
  }
}

function testPostcodeLookup() {
  testData.value = {
    name: 'Test Match',
    description: 'Testing postcode lookup functionality',
    postcode: '00-001', // Warsaw postcode
    country: 'Poland'
  }
}

function testMapUpdate() {
  testData.value = {
    name: 'Test Match',
    description: 'Testing map update functionality',
    address: 'Plac Defilad 1',
    city: 'Warsaw',
    postcode: '00-901',
    country: 'Poland'
  }
}

function onSubmit(data: MatchFormData) {
  lastSubmittedData.value = data
  console.log('Form submitted:', data)
}

function onCancel() {
  console.log('Form cancelled')
}
</script>
