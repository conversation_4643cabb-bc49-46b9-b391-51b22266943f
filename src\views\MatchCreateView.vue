<script setup lang="ts">
import { computed, onMounted, ref } from 'vue'
import { useRouter } from 'vue-router'
import { useI18n } from 'vue-i18n'

import { useMatchesStore, type MatchFormData } from '@/stores/matches'
import { useUserStore } from '@/stores/user'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert'
import { AlertCircle, CheckCircle2, ArrowLeft } from 'lucide-vue-next'
import MatchForm from '@/components/matches/MatchForm.vue'

const { t } = useI18n()
const router = useRouter()
const matchesStore = useMatchesStore()
const userStore = useUserStore()

// Component state
const isSubmitting = ref(false)
const showSuccess = ref(false)
const createdMatchId = ref<number | null>(null)

// Computed properties
const hasOrganizerPrivileges = computed(() => {
  return userStore.userProfile?.organizers && userStore.userProfile.organizers.length > 0
})

const hasActiveOrganizer = computed(() => {
  return !!userStore.activeOrganizer
})

const canCreateMatch = computed(() => {
  return hasOrganizerPrivileges.value && hasActiveOrganizer.value && !isSubmitting.value
})

const organizerError = computed(() => {
  if (!hasOrganizerPrivileges.value) {
    return t('matches.create.error.noPrivileges')
  }
  if (!hasActiveOrganizer.value) {
    return t('matches.create.error.noOrganizer')
  }
  return null
})

// Form submission handler
async function handleFormSubmit(formData: MatchFormData) {
  if (!canCreateMatch.value) {
    return
  }

  isSubmitting.value = true
  matchesStore.clearErrors()

  try {
    const newMatch = await matchesStore.createMatch(formData)
    createdMatchId.value = newMatch.id
    showSuccess.value = true
  } catch (error) {
    console.error('Failed to create match:', error)
    // Error is already handled by the store
  } finally {
    isSubmitting.value = false
  }
}

// Navigation handlers
function handleCancel() {
  router.push({ name: 'organizer-dashboard' })
}

function handleViewMatch() {
  if (createdMatchId.value) {
    router.push({ name: 'match-details', params: { id: createdMatchId.value } })
  }
}

function handleCreateAnother() {
  showSuccess.value = false
  createdMatchId.value = null
  matchesStore.clearErrors()
  matchesStore.clearDraft()
}

function handleGoBack() {
  router.push({ name: 'organizer-dashboard' })
}

// Lifecycle
onMounted(async () => {
  // Ensure user profile is loaded for organizer checks
  if (!userStore.hasProfile) {
    try {
      await userStore.fetchUserProfile()
    } catch (error) {
      console.error('Failed to fetch user profile:', error)
    }
  }

  // Load any existing draft
  matchesStore.loadDraft()
})
</script>

<template>
  <div class="container mx-auto p-6 max-w-4xl">
    <!-- Header -->
    <div class="mb-6">
      <div class="flex items-center gap-4 mb-4">
        <Button
          variant="ghost"
          size="sm"
          @click="handleGoBack"
          class="flex items-center gap-2"
        >
          <ArrowLeft class="h-4 w-4" />
          {{ t('common.back') }}
        </Button>
      </div>

      <div>
        <h1 class="text-3xl font-bold tracking-tight">
          {{ t('matches.create.title') }}
        </h1>
        <p class="text-muted-foreground mt-2">
          {{ t('matches.create.description') }}
        </p>
      </div>
    </div>

    <!-- Success State -->
    <div v-if="showSuccess" class="space-y-6">
      <Alert class="border-green-200 bg-green-50">
        <CheckCircle2 class="h-4 w-4 text-green-600" />
        <AlertTitle class="text-green-800">
          {{ t('matches.create.success.title') }}
        </AlertTitle>
        <AlertDescription class="text-green-700">
          {{ t('matches.create.success.message') }}
        </AlertDescription>
      </Alert>

      <div class="flex gap-4">
        <Button @click="handleViewMatch" class="flex-1">
          {{ t('matches.create.success.viewMatch') }}
        </Button>
        <Button @click="handleCreateAnother" variant="outline" class="flex-1">
          {{ t('matches.create.success.createAnother') }}
        </Button>
      </div>
    </div>

    <!-- Error State - No Organizer Privileges -->
    <div v-else-if="organizerError" class="space-y-6">
      <Alert variant="destructive">
        <AlertCircle class="h-4 w-4" />
        <AlertTitle>{{ t('matches.create.error.title') }}</AlertTitle>
        <AlertDescription>{{ organizerError }}</AlertDescription>
      </Alert>

      <Button @click="handleGoBack" variant="outline">
        {{ t('common.goBack') }}
      </Button>
    </div>

    <!-- Main Form -->
    <div v-else class="space-y-6">
      <!-- General Error Display -->
      <Alert v-if="matchesStore.error" variant="destructive">
        <AlertCircle class="h-4 w-4" />
        <AlertTitle>{{ t('matches.create.error.title') }}</AlertTitle>
        <AlertDescription>
          {{ matchesStore.error.message || t('matches.create.error.message') }}
        </AlertDescription>
      </Alert>

      <!-- Match Creation Form -->
      <MatchForm
        :is-loading="isSubmitting"
        @submit="handleFormSubmit"
        @cancel="handleCancel"
      />
    </div>
  </div>
</template>
