<script setup lang="ts">
import { useI18n } from 'vue-i18n'

const { t } = useI18n()
</script>

<template>
  <div class="container mx-auto p-6">
    <h1 class="text-3xl font-bold tracking-tight mb-6">
      {{ t('navigation.organizer.manageMatches') }}
    </h1>
    <div class="text-center py-12 text-muted-foreground">
      <p class="text-lg mb-4">Organizer Matches Management</p>
      <p class="text-sm">
        This view will be implemented in future tasks.
      </p>
    </div>
  </div>
</template>
