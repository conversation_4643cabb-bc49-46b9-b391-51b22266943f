<script setup lang="ts">
import {
  SidebarGroup,
  SidebarGroupLabel,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
} from '@/components/ui/sidebar'
import {
  Plus,
  Calendar,
  Users,
  Settings,
  BarChart3
} from 'lucide-vue-next'
import { useRoute } from 'vue-router'
import { useI18n } from 'vue-i18n'
import { useUserStore } from '@/stores/user'
import { computed } from 'vue'

const { t } = useI18n()
const route = useRoute()
const userStore = useUserStore()

// Only show organizer navigation if user has organizer privileges and active organizer
const showOrganizerNav = computed(() => {
  return userStore.activeOrganizer !== null
})

// Organizer-specific menu items
const organizerItems = [
  {
    title: t('navigation.organizer.createMatch'),
    url: '/organizer/matches/create',
    icon: Plus,
  },
  {
    title: t('navigation.organizer.manageMatches'),
    url: '/organizer/matches',
    icon: Calendar,
  },
  {
    title: t('navigation.organizer.participants'),
    url: '/organizer/participants',
    icon: Users,
  },
  {
    title: t('navigation.organizer.reports'),
    url: '/organizer/reports',
    icon: BarChart3,
  },
  {
    title: t('navigation.organizer.settings'),
    url: '/organizer/settings',
    icon: Settings,
  },
]
</script>

<template>
  <SidebarGroup v-if="showOrganizerNav">
    <SidebarGroupLabel>{{ t('navigation.organizer.title') }}</SidebarGroupLabel>
    <SidebarMenu>
      <SidebarMenuItem v-for="item in organizerItems" :key="item.title">
        <SidebarMenuButton
          as-child
          :tooltip="item.title"
          :data-active="item.url === route.path || route.path.startsWith(item.url + '/')"
        >
          <router-link :to="item.url">
            <component :is="item.icon" v-if="item.icon" />
            <span>{{ item.title }}</span>
          </router-link>
        </SidebarMenuButton>
      </SidebarMenuItem>
    </SidebarMenu>
  </SidebarGroup>
</template>
