# Requirements Document

## Introduction

This feature adds match creation functionality to the Archery Points application, allowing organizers to create new matches with configurable divisions. The feature includes a dedicated organizer menu in the left sidebar that is only visible when users have organizer privileges and have selected a current organizer. This enables match organizers to efficiently set up and manage archery matches through an intuitive form-based interface.

## Requirements

### Requirement 1

**User Story:** As an organizer, I want to create new matches through a dedicated form interface, so that I can set up archery events for participants to register for.

#### Acceptance Criteria

1. WH<PERSON> an organizer navigates to the match creation page THEN the system SHALL display a comprehensive form with all necessary match fields
2. WHEN an organizer fills out the match form THEN the system SHALL validate all required fields before allowing submission
3. WHEN an organizer submits a valid match form THEN the system SHALL create the match using the current organizer from the user store
4. WHEN a match is successfully created THEN the system SHALL redirect the organizer to the match details or management page
5. IF the form contains validation errors THEN the system SHALL display clear error messages for each invalid field

### Requirement 2

**User Story:** As an organizer, I want to configure divisions for my match during creation, so that participants can compete in appropriate categories based on their skill level or equipment type.

#### Acceptance Criteria

1. WH<PERSON> creating a match THEN the system SHALL provide options to add multiple divisions
2. WHEN configuring divisions THEN the system SHALL allow setting division names, categories, and rules
3. WHEN adding divisions THEN the system SHALL validate that each division has required information
4. WHEN removing divisions THEN the system SHALL confirm the action to prevent accidental deletion
5. IF no divisions are configured THEN the system SHALL require at least one division before allowing match creation

### Requirement 3

**User Story:** As an organizer, I want access to an organizer-specific menu in the sidebar, so that I can quickly navigate to organizer functions when I have organizer privileges.

#### Acceptance Criteria

1. WHEN a user has organizer privileges AND has selected a current organizer THEN the system SHALL display an organizer menu section in the left sidebar
2. WHEN a user does not have organizer privileges THEN the system SHALL NOT display the organizer menu section
3. WHEN a user has organizer privileges but no current organizer is selected THEN the system SHALL NOT display the organizer menu section
4. WHEN the organizer menu is displayed THEN the system SHALL include navigation links to organizer-specific pages including match creation
5. WHEN navigating through organizer menu items THEN the system SHALL highlight the currently active organizer page

### Requirement 4

**User Story:** As an organizer, I want to select a federation and subsequently choose from predefined divisions within that federation during match creation, so that I can ensure matches comply with federation-specific rules and division structures.

#### Acceptance Criteria

1. WHEN accessing the match creation form THEN the system SHALL provide a federation selection dropdown populated from the federations service
2. WHEN selecting a federation THEN the system SHALL load and display the available age divisions and style divisions predefined for that federation
3. WHEN federation divisions are loaded THEN the system SHALL allow the organizer to select multiple divisions from the federation's available options
4. WHEN no federation is selected THEN the system SHALL disable division selection and display appropriate guidance
5. WHEN a federation is changed THEN the system SHALL clear previously selected divisions and reload divisions for the new federation
6. WHEN submitting the match form THEN the system SHALL validate that selected divisions belong to the chosen federation
7. IF federation data fails to load THEN the system SHALL display an error message and provide retry functionality

### Requirement 5

**User Story:** As an organizer, I want the system to use my current organizer context when creating matches, so that matches are properly associated with my organization.

#### Acceptance Criteria

1. WHEN creating a match THEN the system SHALL automatically use the current organizer from the user store
2. WHEN no current organizer is selected THEN the system SHALL prevent match creation and prompt organizer selection
3. WHEN the current organizer changes THEN the system SHALL update the match creation context accordingly
4. WHEN submitting a match THEN the system SHALL validate that the current organizer has permission to create matches
5. IF the organizer context is invalid THEN the system SHALL display an appropriate error message and prevent submission

### Requirement 6

**User Story:** As an organizer, I want the match creation form to be intuitive and user-friendly, so that I can efficiently set up matches without confusion or errors.

#### Acceptance Criteria

1. WHEN accessing the match creation form THEN the system SHALL organize fields into logical sections (basic info, federation/divisions, settings)
2. WHEN filling out form fields THEN the system SHALL provide helpful placeholder text and field descriptions
3. WHEN interacting with form elements THEN the system SHALL provide immediate feedback for validation errors
4. WHEN saving form progress THEN the system SHALL allow draft saving to prevent data loss
5. IF the form is complex THEN the system SHALL provide a progress indicator or step-by-step wizard interface

### Requirement 7

**User Story:** As a system, I need a federations store to manage federation data and support federation-based division selection, so that organizers can create matches with proper federation compliance.

#### Acceptance Criteria

1. WHEN the application initializes THEN the system SHALL provide a federations store using the feathers-client pattern like other stores
2. WHEN the federations store is accessed THEN the system SHALL provide methods to fetch federations from the federations service
3. WHEN federation data is retrieved THEN the system SHALL expose federation properties including ageDivisions, styleDivisions, and disciplines
4. WHEN federation data is needed THEN the system SHALL cache federation information to improve performance
5. WHEN federation data changes THEN the system SHALL provide reactive updates to components using the store
6. IF federation service requests fail THEN the system SHALL handle errors gracefully and provide appropriate error states
