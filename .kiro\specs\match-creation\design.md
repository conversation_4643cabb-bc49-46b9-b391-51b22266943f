# Design Document

## Overview

The match creation feature adds comprehensive functionality for organizers to create new archery matches through a dedicated form interface. The design integrates seamlessly with the existing Vue 3 + TypeScript architecture, utilizing the current user store's organizer context and extending the sidebar navigation with organizer-specific functionality.

The feature consists of three main components:

1. **Match Creation Form** - A comprehensive form for setting up new matches
2. **Division Configuration Interface** - Dynamic form sections for configuring match divisions
3. **Organizer Sidebar Menu** - Conditional navigation menu for organizer-specific functions

## Architecture

### Component Structure

```typescript
src/
├── components/
│   ├── matches/
│   │   ├── MatchForm.vue                 # Main match creation form
│   │   ├── DivisionConfigForm.vue        # Division configuration component
│   │   └── MatchFormSections.vue         # Form section components
│   ├── layout/
│   │   ├── SidebarLeft.vue              # Extended with organizer menu
│   │   └── NavOrganizer.vue             # New organizer navigation component
├── views/
│   └── MatchCreateView.vue               # Main page view
├── stores/
│   └── matches.ts                        # Match state management
└── router/
    └── index.ts                         # Extended with organizer routes
```

### Data Flow

1. **User Authentication Check** → Verify organizer privileges
2. **Current Organizer Validation** → Ensure active organizer is selected
3. **Form State Management** → Handle form data and validation
4. **API Integration** → Submit match data to backend
5. **Navigation** → Redirect to match management after creation

## Components and Interfaces

### MatchCreateView.vue

Main page component that orchestrates the match creation process.

**Props:** None
**Emits:** None
**Key Features:**

- Route guard for organizer authentication
- Integration with MainLayout
- Form submission handling
- Success/error state management

### MatchForm.vue

Core form component handling match details and division configuration.

**Props:**

```typescript
interface MatchFormProps {
  initialData?: Partial<MatchData>
  isLoading?: boolean
}
```

**Emits:**

```typescript
interface MatchFormEmits {
  submit: [data: MatchFormData]
  cancel: []
}
```

**Key Features:**

- Multi-section form layout (Basic Info, Divisions, Settings)
- Real-time validation using VueUse
- Draft saving capability
- Progress indicator

### DivisionConfigForm.vue

Dynamic component for managing match divisions.

**Props:**

```typescript
interface DivisionConfigProps {
  divisions: Division[]
  errors?: Record<string, string>
}
```

**Emits:**

```typescript
interface DivisionConfigEmits {
  'update:divisions': [divisions: Division[]]
}
```

**Key Features:**

- Add/remove divisions dynamically
- Division validation
- Category and rules configuration
- Drag-and-drop reordering

### NavOrganizer.vue

New navigation component for organizer-specific menu items.

**Props:**

```typescript
import type { Organizer } from '@/api/feathers-client'

interface NavOrganizerProps {
  organizer: Organizer
  isCollapsed?: boolean
}
```

**Key Features:**

- Conditional rendering based on organizer privileges
- Integration with existing sidebar structure
- Active route highlighting
- Organizer context display

## Data Models

### Match Form Data

The match creation will use existing feathers client types:

```typescript
import type { 
  Match, MatchData, MatchQuery, MatchPatch,
  MatchRegistration, MatchRegistrationData, MatchRegistrationQuery, MatchRegistrationPatch,
  Organizer, OrganizerData, OrganizerQuery, OrganizerPatch 
} from '@/api/feathers-client'

// Match form extends MatchData for creation
interface MatchFormData extends Omit<MatchData, 'id' | 'organizerId'> {
  // Additional form-specific fields if needed
  divisions: DivisionFormData[]
  settings: MatchSettings
}

// Division configuration using existing patterns
interface DivisionFormData {
  name: string
  category: string
  ageMin?: number
  ageMax?: number
  gender?: 'male' | 'female' | 'mixed'
  equipmentType: string
  rules?: string
  maxParticipants?: number
  registrationFee?: number
}

// Match-specific settings
interface MatchSettings {
  allowLateRegistration: boolean
  requireLicense: boolean
  allowEquipmentChange: boolean
  publicResults: boolean
  emailNotifications: boolean
}
```

## Error Handling

### Form Validation

- **Client-side validation** using VueUse composables
- **Real-time field validation** with immediate feedback
- **Cross-field validation** for date ranges and division conflicts
- **Custom validation rules** for archery-specific requirements

### API Error Handling

- **Network errors** with retry mechanism
- **Validation errors** from backend with field mapping
- **Authentication errors** with redirect to login
- **Permission errors** with clear messaging

### User Experience

- **Loading states** during form submission
- **Success notifications** with next steps
- **Error messages** with actionable guidance
- **Draft recovery** after page reload

## Testing Strategy

### Unit Tests

- **Form validation logic** with various input scenarios
- **Division management** add/remove/update operations
- **Data transformation** between form and API formats
- **Error handling** for different failure modes

### Integration Tests

- **Form submission flow** end-to-end
- **Organizer privilege checks** and navigation
- **API integration** with mock responses
- **Route navigation** and guards

### E2E Tests

- **Complete match creation** workflow
- **Division configuration** scenarios
- **Error recovery** and validation
- **Cross-browser compatibility**

## Implementation Notes

### Existing Integration Points

- **User Store**: Leverage `activeOrganizer` computed property
- **Router**: Extend existing route structure with organizer routes
- **Sidebar**: Modify `SidebarLeft.vue` to include conditional organizer menu
- **API Client**: Use existing `api.matches` service directly from stores
- **i18n**: Add match-related translations to existing structure
- **Store Pattern**: Follow existing store patterns using feathers client directly, no service layer needed

### Form Architecture

- **Composition API** with `<script setup>` syntax
- **TypeScript** for all props and emits
- **Tailwind CSS** with shadcn-vue components
- **VueUse** for form validation and state management
- **Pinia** for match state management

### Navigation Enhancement

- **Conditional rendering** based on organizer privileges
- **Route guards** for organizer-only pages
- **Active state management** for organizer menu items
- **Responsive design** for mobile organizer workflows

### Performance Considerations

- **Lazy loading** for match creation route
- **Form field debouncing** for validation
- **Draft autosave** with throttling
- **Component code splitting** for organizer features