# Implementation Plan

- [x] 1. Set up match store and types
  - Create Pinia store for match state management using existing patterns
  - Define TypeScript interfaces extending feathers client types (MatchData, MatchRegistration, Organizer)
  - Implement store actions for match creation, validation, and error handling
  - _Requirements: 1.1, 1.3, 5.1, 5.4_

- [x] 2. Create organizer navigation component
  - [x] 2.1 Implement NavOrganizer component with conditional rendering
    - Create NavOrganizer.vue component using existing sidebar patterns
    - Add conditional rendering based on user store's activeOrganizer
    - Implement organizer-specific menu items with proper routing
    - _Requirements: 3.1, 3.4, 5.1_

  - [x] 2.2 Integrate organizer menu into existing sidebar
    - Modify SidebarLeft.vue to include NavOrganizer component
    - Add conditional display logic based on organizer privileges
    - Ensure proper integration with existing navigation structure
    - _Requirements: 3.1, 3.2, 3.3_

- [x] 3. Create match creation route and view
  - [x] 3.1 Add organizer routes to router configuration
    - Extend router/index.ts with organizer-specific routes
    - Implement route guards for organizer authentication
    - Add match creation route with proper meta information
    - _Requirements: 3.4, 5.2, 5.4_

  - [x] 3.2 Create MatchCreateView component
    - Implement main page component using MainLayout
    - Add organizer privilege validation and error handling
    - Integrate with match store for form state management
    - Handle success/error states and navigation after creation
    - _Requirements: 1.4, 4.1, 5.2, 5.5_

- [-] 4. Implement match form components
  - [x] 4.1 Create base MatchForm component
    - Build main form component with TypeScript props and emits
    - Implement multi-section form layout (Basic Info, Divisions, Settings)
    - Add form validation using VueUse composables
    - Integrate with shadcn-vue form components and Tailwind styling
    - _Requirements: 1.1, 1.2, 4.1, 4.2_

  - [x] 4.2 Implement division configuration component
    - Create DivisionConfigForm.vue for dynamic division management
    - Add functionality to add, remove, and edit divisions
    - Implement division validation and error display
    - Add drag-and-drop reordering for divisions
    - _Requirements: 2.1, 2.2, 2.3, 2.4, 2.5_

  - [ ] 4.3 Add form sections and validation
    - Create MatchFormSections.vue for organized form layout
    - Implement real-time validation with immediate feedback
    - Add cross-field validation for date ranges and division conflicts
    - Create progress indicator for multi-step form experience
    - _Requirements: 1.2, 4.2, 4.3, 4.4_

- [ ] 5. Implement form submission and API integration
  - [ ] 5.1 Add match creation API integration
    - Implement createMatch action in match store
    - Use existing api.matches service for match creation
    - Handle API responses and error mapping to form fields
    - Add loading states and success notifications
    - _Requirements: 1.3, 1.4, 5.1, 5.3_

  - [ ] 5.2 Implement draft saving functionality
    - Add autosave functionality for form data persistence
    - Implement draft recovery after page reload
    - Create draft management in match store
    - Add user feedback for draft save status
    - _Requirements: 4.4_

- [ ] 6. Add internationalization support
  - Extend existing i18n configuration with match-related translations
  - Add English translations for all form labels, validation messages, and UI text
  - Implement translation keys in all match components
  - Ensure consistent translation patterns with existing codebase
  - _Requirements: 4.2_

- [ ] 7. Create comprehensive form validation
  - [ ] 7.1 Implement client-side validation rules
    - Create validation composables for match-specific rules
    - Add archery-specific validation (federation rules, equipment types)
    - Implement date validation for registration deadlines and event dates
    - Add participant limit and fee validation
    - _Requirements: 1.2, 2.3, 4.3_

  - [ ] 7.2 Add error handling and user feedback
    - Implement comprehensive error handling for API failures
    - Add user-friendly error messages with actionable guidance
    - Create retry mechanisms for network failures
    - Add validation error display with field highlighting
    - _Requirements: 1.5, 4.3, 5.5_

- [ ] 8. Write comprehensive tests
  - [ ] 8.1 Create unit tests for match store
    - Test match creation, validation, and error handling logic
    - Test division management operations (add, remove, update)
    - Test form data transformation and API integration
    - Test draft saving and recovery functionality
    - _Requirements: 1.1, 1.3, 2.1, 2.2, 2.3_

  - [ ] 8.2 Create component tests for form components
    - Test MatchForm component with various input scenarios
    - Test DivisionConfigForm dynamic operations and validation
    - Test NavOrganizer conditional rendering and navigation
    - Test form validation and error display
    - _Requirements: 1.2, 2.4, 3.1, 4.2, 4.3_

  - [ ] 8.3 Add integration tests for complete workflow
    - Test end-to-end match creation flow
    - Test organizer privilege checks and navigation
    - Test form submission with API integration
    - Test error recovery and validation scenarios
    - _Requirements: 1.4, 3.2, 5.2, 5.4, 5.5_
