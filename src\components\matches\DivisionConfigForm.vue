<template>
  <div class="space-y-6">
    <!-- Available Divisions from Federation -->
    <div v-if="availableAgeDivisions.length > 0 || availableStyleDivisions.length > 0" class="space-y-4">
      <div class="flex items-center justify-between">
        <h4 class="text-sm font-medium">{{ t('matches.form.divisions.available') }}</h4>
        <Button
          type="button"
          variant="outline"
          size="sm"
          @click="addCustomDivision"
        >
          {{ t('matches.form.divisions.addCustom') }}
        </Button>
      </div>

      <!-- Age Divisions -->
      <div v-if="availableAgeDivisions.length > 0" class="space-y-2">
        <Label class="text-sm font-medium">{{ t('matches.form.divisions.ageDivisions') }}</Label>
        <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-2">
          <div
            v-for="ageDivision in availableAgeDivisions"
            :key="`age-${ageDivision.name}`"
            class="flex items-center space-x-2"
          >
            <Checkbox
              :id="`age-${ageDivision.name}`"
              :checked="isAgeDivisionSelected(ageDivision)"
              @update:checked="(checked) => toggleAgeDivision(ageDivision, checked)"
            />
            <Label
              :for="`age-${ageDivision.name}`"
              class="text-sm cursor-pointer"
            >
              {{ ageDivision.name }}
              <span v-if="ageDivision.min || ageDivision.max" class="text-muted-foreground">
                ({{ formatAgeRange(ageDivision.min, ageDivision.max) }})
              </span>
            </Label>
          </div>
        </div>
      </div>

      <!-- Style Divisions -->
      <div v-if="availableStyleDivisions.length > 0" class="space-y-2">
        <Label class="text-sm font-medium">{{ t('matches.form.divisions.styleDivisions') }}</Label>
        <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-2">
          <div
            v-for="styleDivision in availableStyleDivisions"
            :key="`style-${styleDivision.name}`"
            class="flex items-center space-x-2"
          >
            <Checkbox
              :id="`style-${styleDivision.name}`"
              :checked="isStyleDivisionSelected(styleDivision)"
              @update:checked="(checked) => toggleStyleDivision(styleDivision, checked)"
            />
            <Label
              :for="`style-${styleDivision.name}`"
              class="text-sm cursor-pointer"
            >
              {{ styleDivision.name }}
              <span v-if="styleDivision.bow_types?.length" class="text-muted-foreground">
                ({{ styleDivision.bow_types.join(', ') }})
              </span>
            </Label>
          </div>
        </div>
      </div>
    </div>

    <!-- Selected Divisions Configuration -->
    <div v-if="divisions.length > 0" class="space-y-4">
      <div class="flex items-center justify-between">
        <h4 class="text-sm font-medium">{{ t('matches.form.divisions.selected') }}</h4>
        <span class="text-sm text-muted-foreground">
          {{ t('matches.form.divisions.count', { count: divisions.length }) }}
        </span>
      </div>

      <div class="space-y-4">
        <Card
          v-for="(division, index) in divisions"
          :key="`division-${index}`"
          :class="[
            'relative cursor-move transition-all duration-200',
            {
              'opacity-50 scale-95': draggedIndex === index,
              'border-primary border-2': draggedIndex !== null && draggedIndex !== index
            }
          ]"
          :draggable="true"
          @dragstart="handleDragStart(index, $event)"
          @dragover.prevent
          @drop="handleDrop(index, $event)"
          @dragenter.prevent
        >
          <CardHeader class="pb-4">
            <div class="flex items-center justify-between">
              <div class="flex items-center gap-2">
                <div class="cursor-move text-muted-foreground hover:text-foreground">
                  <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                    <circle cx="9" cy="12" r="1"/>
                    <circle cx="9" cy="5" r="1"/>
                    <circle cx="9" cy="19" r="1"/>
                    <circle cx="15" cy="12" r="1"/>
                    <circle cx="15" cy="5" r="1"/>
                    <circle cx="15" cy="19" r="1"/>
                  </svg>
                </div>
                <CardTitle class="text-base">{{ division.name }}</CardTitle>
              </div>
              <div class="flex items-center gap-2">
                <Button
                  type="button"
                  variant="ghost"
                  size="sm"
                  @click="moveDivision(index, -1)"
                  :disabled="index === 0"
                >
                  ↑
                </Button>
                <Button
                  type="button"
                  variant="ghost"
                  size="sm"
                  @click="moveDivision(index, 1)"
                  :disabled="index === divisions.length - 1"
                >
                  ↓
                </Button>
                <Button
                  type="button"
                  variant="ghost"
                  size="sm"
                  @click="removeDivision(index)"
                  class="text-destructive hover:text-destructive"
                >
                  ×
                </Button>
              </div>
            </div>
          </CardHeader>

          <CardContent class="space-y-4">
            <!-- Division Name (editable for custom divisions) -->
            <div v-if="division.isCustom" class="space-y-2">
              <Label :for="`division-name-${index}`">{{ t('matches.form.divisions.name') }}</Label>
              <Input
                :id="`division-name-${index}`"
                v-model="division.name"
                :placeholder="t('matches.form.divisions.namePlaceholder')"
                :class="{ 'border-destructive': errors[`division-${index}`]?.includes('name') }"
                @blur="validateDivision(index)"
              />
            </div>

            <!-- Category and Equipment Type -->
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div class="space-y-2">
                <Label :for="`division-category-${index}`">{{ t('matches.form.divisions.category') }}</Label>
                <select
                  :id="`division-category-${index}`"
                  v-model="division.category"
                  class="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                  @change="validateDivision(index)"
                >
                  <option value="">{{ t('matches.form.divisions.selectCategory') }}</option>
                  <option value="age">{{ t('matches.form.divisions.categories.age') }}</option>
                  <option value="style">{{ t('matches.form.divisions.categories.style') }}</option>
                  <option value="mixed">{{ t('matches.form.divisions.categories.mixed') }}</option>
                </select>
              </div>

              <div class="space-y-2">
                <Label :for="`division-equipment-${index}`">{{ t('matches.form.divisions.equipmentType') }}</Label>
                <Input
                  :id="`division-equipment-${index}`"
                  v-model="division.equipmentType"
                  :placeholder="t('matches.form.divisions.equipmentPlaceholder')"
                  :class="{ 'border-destructive': errors[`division-${index}`]?.includes('equipment') }"
                  @blur="validateDivision(index)"
                />
              </div>
            </div>

            <!-- Age Range (for age divisions) -->
            <div v-if="division.category === 'age'" class="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div class="space-y-2">
                <Label :for="`division-age-min-${index}`">{{ t('matches.form.divisions.ageMin') }}</Label>
                <Input
                  :id="`division-age-min-${index}`"
                  v-model.number="division.ageMin"
                  type="number"
                  min="0"
                  max="100"
                  :placeholder="t('matches.form.divisions.ageMinPlaceholder')"
                  @blur="validateDivision(index)"
                />
              </div>

              <div class="space-y-2">
                <Label :for="`division-age-max-${index}`">{{ t('matches.form.divisions.ageMax') }}</Label>
                <Input
                  :id="`division-age-max-${index}`"
                  v-model.number="division.ageMax"
                  type="number"
                  min="0"
                  max="100"
                  :placeholder="t('matches.form.divisions.ageMaxPlaceholder')"
                  @blur="validateDivision(index)"
                />
              </div>
            </div>

            <!-- Gender Selection -->
            <div class="space-y-2">
              <Label>{{ t('matches.form.divisions.gender') }}</Label>
              <div class="flex gap-4">
                <div class="flex items-center space-x-2">
                  <input
                    :id="`gender-mixed-${index}`"
                    v-model="division.gender"
                    type="radio"
                    value="mixed"
                    :name="`gender-${index}`"
                    class="w-4 h-4 text-primary bg-background border-input focus:ring-ring focus:ring-2"
                  />
                  <Label :for="`gender-mixed-${index}`" class="text-sm">{{ t('matches.form.divisions.genders.mixed') }}</Label>
                </div>
                <div class="flex items-center space-x-2">
                  <input
                    :id="`gender-male-${index}`"
                    v-model="division.gender"
                    type="radio"
                    value="male"
                    :name="`gender-${index}`"
                    class="w-4 h-4 text-primary bg-background border-input focus:ring-ring focus:ring-2"
                  />
                  <Label :for="`gender-male-${index}`" class="text-sm">{{ t('matches.form.divisions.genders.male') }}</Label>
                </div>
                <div class="flex items-center space-x-2">
                  <input
                    :id="`gender-female-${index}`"
                    v-model="division.gender"
                    type="radio"
                    value="female"
                    :name="`gender-${index}`"
                    class="w-4 h-4 text-primary bg-background border-input focus:ring-ring focus:ring-2"
                  />
                  <Label :for="`gender-female-${index}`" class="text-sm">{{ t('matches.form.divisions.genders.female') }}</Label>
                </div>
              </div>
            </div>

            <!-- Optional Settings -->
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div class="space-y-2">
                <Label :for="`division-max-participants-${index}`">{{ t('matches.form.divisions.maxParticipants') }}</Label>
                <Input
                  :id="`division-max-participants-${index}`"
                  v-model.number="division.maxParticipants"
                  type="number"
                  min="1"
                  :placeholder="t('matches.form.divisions.maxParticipantsPlaceholder')"
                  @blur="validateDivision(index)"
                />
              </div>

              <div class="space-y-2">
                <Label :for="`division-fee-${index}`">{{ t('matches.form.divisions.registrationFee') }}</Label>
                <Input
                  :id="`division-fee-${index}`"
                  v-model.number="division.registrationFee"
                  type="number"
                  min="0"
                  step="0.01"
                  :placeholder="t('matches.form.divisions.registrationFeePlaceholder')"
                  @blur="validateDivision(index)"
                />
              </div>
            </div>

            <!-- Rules (optional) -->
            <div class="space-y-2">
              <Label :for="`division-rules-${index}`">{{ t('matches.form.divisions.rules') }}</Label>
              <textarea
                :id="`division-rules-${index}`"
                v-model="division.rules"
                :placeholder="t('matches.form.divisions.rulesPlaceholder')"
                class="flex min-h-[60px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                rows="2"
              />
            </div>

            <!-- Validation Errors -->
            <div v-if="errors[`division-${index}`]?.length" class="space-y-1">
              <p
                v-for="error in errors[`division-${index}`]"
                :key="error"
                class="text-sm text-destructive"
              >
                {{ error }}
              </p>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>

    <!-- Empty State -->
    <div v-else class="text-center py-8 text-muted-foreground">
      <p>{{ t('matches.form.divisions.noDivisionsSelected') }}</p>
      <Button
        type="button"
        variant="outline"
        class="mt-4"
        @click="addCustomDivision"
      >
        {{ t('matches.form.divisions.addFirst') }}
      </Button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, ref, watch } from 'vue'
import { useI18n } from 'vue-i18n'

import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Checkbox } from '@/components/ui/checkbox'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'

import { useFederationsStore, type AgeDivision, type StyleDivision } from '@/stores/federations'
import { type DivisionFormData } from '@/stores/matches'

// Extended division interface for internal use
interface ExtendedDivisionFormData extends DivisionFormData {
  isCustom?: boolean
}

// Props and emits
interface Props {
  divisions: DivisionFormData[]
  federationId: number
  errors?: Record<string, string[]>
}

interface Emits {
  (e: 'update:divisions', divisions: DivisionFormData[]): void
  (e: 'validate', errors: Record<string, string[]>): void
}

const props = withDefaults(defineProps<Props>(), {
  errors: () => ({})
})

const emit = defineEmits<Emits>()

// Composables
const { t } = useI18n()
const federationsStore = useFederationsStore()

// Local state
const errors = ref<Record<string, string[]>>({ ...props.errors })
const draggedIndex = ref<number | null>(null)

// Computed properties
const availableAgeDivisions = computed(() =>
  federationsStore.getFederationAgeDivisions(props.federationId)
)

const availableStyleDivisions = computed(() =>
  federationsStore.getFederationStyleDivisions(props.federationId)
)

const divisions = computed({
  get: () => props.divisions as ExtendedDivisionFormData[],
  set: (value) => emit('update:divisions', value)
})

// Helper functions
function formatAgeRange(min?: number, max?: number): string {
  if (min && max) return `${min}-${max}`
  if (min) return `${min}+`
  if (max) return `≤${max}`
  return ''
}

function isAgeDivisionSelected(ageDivision: AgeDivision): boolean {
  return divisions.value.some(d =>
    d.name === ageDivision.name &&
    d.category === 'age' &&
    !d.isCustom
  )
}

function isStyleDivisionSelected(styleDivision: StyleDivision): boolean {
  return divisions.value.some(d =>
    d.name === styleDivision.name &&
    d.category === 'style' &&
    !d.isCustom
  )
}

function toggleAgeDivision(ageDivision: AgeDivision, checked: boolean | string) {
  const isChecked = checked === true || checked === 'true'
  if (isChecked) {
    const newDivision: ExtendedDivisionFormData = {
      name: ageDivision.name,
      category: 'age',
      ageMin: ageDivision.min,
      ageMax: ageDivision.max,
      gender: 'mixed',
      equipmentType: '',
      isCustom: false
    }
    divisions.value = [...divisions.value, newDivision]
  } else {
    divisions.value = divisions.value.filter(d =>
      !(d.name === ageDivision.name && d.category === 'age' && !d.isCustom)
    )
  }
  validateAllDivisions()
}

function toggleStyleDivision(styleDivision: StyleDivision, checked: boolean | string) {
  const isChecked = checked === true || checked === 'true'
  if (isChecked) {
    const newDivision: ExtendedDivisionFormData = {
      name: styleDivision.name,
      category: 'style',
      gender: 'mixed',
      equipmentType: styleDivision.bow_types?.[0] || '',
      isCustom: false
    }
    divisions.value = [...divisions.value, newDivision]
  } else {
    divisions.value = divisions.value.filter(d =>
      !(d.name === styleDivision.name && d.category === 'style' && !d.isCustom)
    )
  }
  validateAllDivisions()
}

function addCustomDivision() {
  const newDivision: ExtendedDivisionFormData = {
    name: '',
    category: '',
    gender: 'mixed',
    equipmentType: '',
    isCustom: true
  }
  divisions.value = [...divisions.value, newDivision]
}

function removeDivision(index: number) {
  divisions.value = divisions.value.filter((_, i) => i !== index)

  // Remove errors for this division
  const newErrors = { ...errors.value }
  delete newErrors[`division-${index}`]

  // Reindex remaining division errors
  const reindexedErrors: Record<string, string[]> = {}
  Object.keys(newErrors).forEach(key => {
    if (key.startsWith('division-')) {
      const divisionIndex = parseInt(key.split('-')[1])
      if (divisionIndex > index) {
        reindexedErrors[`division-${divisionIndex - 1}`] = newErrors[key]
      } else if (divisionIndex < index) {
        reindexedErrors[key] = newErrors[key]
      }
    } else {
      reindexedErrors[key] = newErrors[key]
    }
  })

  errors.value = reindexedErrors
  emit('validate', errors.value)
}

function moveDivision(index: number, direction: number) {
  const newIndex = index + direction
  if (newIndex < 0 || newIndex >= divisions.value.length) return

  const newDivisions = [...divisions.value]
  const temp = newDivisions[index]
  newDivisions[index] = newDivisions[newIndex]
  newDivisions[newIndex] = temp

  divisions.value = newDivisions
}

function validateDivision(index: number) {
  const division = divisions.value[index]
  const divisionErrors: string[] = []

  if (!division.name?.trim()) {
    divisionErrors.push(t('matches.form.validation.divisionNameRequired'))
  }

  if (!division.category?.trim()) {
    divisionErrors.push(t('matches.form.validation.divisionCategoryRequired'))
  }

  if (!division.equipmentType?.trim()) {
    divisionErrors.push(t('matches.form.validation.divisionEquipmentRequired'))
  }

  if (division.ageMin && division.ageMax && division.ageMin >= division.ageMax) {
    divisionErrors.push(t('matches.form.validation.divisionAgeRangeInvalid'))
  }

  if (division.maxParticipants && division.maxParticipants <= 0) {
    divisionErrors.push(t('matches.form.validation.divisionMaxParticipantsInvalid'))
  }

  if (division.registrationFee && division.registrationFee < 0) {
    divisionErrors.push(t('matches.form.validation.divisionRegistrationFeeInvalid'))
  }

  if (divisionErrors.length > 0) {
    errors.value[`division-${index}`] = divisionErrors
  } else {
    const newErrors = { ...errors.value }
    delete newErrors[`division-${index}`]
    errors.value = newErrors
  }

  emit('validate', errors.value)
}

function validateAllDivisions() {
  divisions.value.forEach((_, index) => {
    validateDivision(index)
  })
}

// Drag and drop handlers
function handleDragStart(index: number, event: DragEvent) {
  draggedIndex.value = index
  if (event.dataTransfer) {
    event.dataTransfer.effectAllowed = 'move'
    event.dataTransfer.setData('text/html', index.toString())
  }
}

function handleDrop(targetIndex: number, event: DragEvent) {
  event.preventDefault()

  if (draggedIndex.value === null || draggedIndex.value === targetIndex) {
    return
  }

  const newDivisions = [...divisions.value]
  const draggedItem = newDivisions[draggedIndex.value]

  // Remove the dragged item
  newDivisions.splice(draggedIndex.value, 1)

  // Insert at the new position
  const insertIndex = draggedIndex.value < targetIndex ? targetIndex - 1 : targetIndex
  newDivisions.splice(insertIndex, 0, draggedItem)

  divisions.value = newDivisions
  draggedIndex.value = null

  // Revalidate after reordering
  validateAllDivisions()
}

// Watch for external error changes
watch(() => props.errors, (newErrors) => {
  errors.value = { ...newErrors }
}, { deep: true })

// Watch divisions for validation
watch(divisions, () => {
  validateAllDivisions()
}, { deep: true })
</script>
