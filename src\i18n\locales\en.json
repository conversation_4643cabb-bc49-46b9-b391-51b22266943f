{"common": {"loading": "Loading...", "error": "Error", "save": "Save", "cancel": "Cancel", "delete": "Delete", "edit": "Edit", "close": "Close", "search": "Search", "filter": "Filter", "clear": "Clear", "apply": "Apply", "reset": "Reset", "submit": "Submit", "back": "Back", "next": "Next", "previous": "Previous", "yes": "Yes", "no": "No", "ok": "OK", "na": "N/A", "total": "Total", "upcoming": "Upcoming", "completed": "Completed", "required": "Required", "notRequired": "Not Required", "goBack": "Go Back", "day": "day | days"}, "navigation": {"home": "Home", "matches": "Matches", "tournaments": "Tournaments", "about": "About", "login": "<PERSON><PERSON>", "logout": "Logout", "profile": "Profile", "settings": "Settings", "search": "Search", "mySignups": "My Signups", "watched": "Watched", "results": "Results", "archive": "Archive", "statistics": "Statistics", "favourites": "Favourites", "messages": "Messages", "organizer": {"title": "Organizer", "createMatch": "Create Match", "manageMatches": "Manage Matches", "participants": "Participants", "reports": "Reports", "settings": "Settings"}}, "auth": {"login": "<PERSON><PERSON>", "logout": "Logout", "register": "Register", "email": "Email", "password": "Password", "confirmPassword": "Confirm Password", "forgotPassword": "Forgot Password?", "rememberMe": "Remember Me", "signIn": "Sign In", "signUp": "Sign Up", "withdraw": "Withdraw", "orContinueWith": "Or continue with", "googleNotImplemented": "Google (Not Implemented)", "facebookNotImplemented": "Facebook (Not Implemented)", "enterEmailPassword": "Enter your email and password to access your account.", "byClickingContinue": "By clicking continue, you agree to our", "termsOfService": "Terms of Service", "privacyPolicy": "Privacy Policy"}, "app": {"title": "Archery Points", "tagline": "Track your scores, climb the ranks, and become a champion.", "teamName": "The Archery Points Team"}, "views": {"about": "about", "settings": {"title": "Application Settings", "description": "This is the settings page. General application preferences and configurations will be managed here."}, "account": {"title": "Account <PERSON><PERSON>", "description": "This is the account page. User-specific settings and profile information will be displayed and managed here."}, "myMatches": {"title": "My Matches", "description": "Competitions you've signed up for", "loadingMatches": "Loading your matches...", "errorLoadingMatches": "Error loading matches", "noMatchesYet": "No matches yet", "noMatchesDescription": "You haven't signed up for any competitions yet.", "browseMatches": "Browse Matches", "upcomingMatches": "Upcoming Matches", "pastMatches": "Past Matches"}}, "matches": {"signUp": "SIGN UP", "withdraw": "WITHDRAW", "loading": "LOADING...", "loadingMatchDetails": "Loading match details...", "errorLoadingMatch": "Error loading match", "viewFullDetails": "View Full Details", "competitorsRivals": "Competitors/Rivals", "equipmentCategories": "Equipment Categories", "ageCategories": "Age Categories", "info": "Info", "organizer": "Organizer", "competitionRank": "Competition Rank", "competitionType": "Competition Type", "federation": "Federation", "license": "License", "phone": "Phone", "email": "Email", "distanceToEvent": "Distance to event", "sunrise": "Sunrise", "sunset": "Sunset", "temperature": "Temperature", "weather": "Weather", "min": "Min", "max": "Max", "age": "Age", "ageRange": "Age: {min} - {max}", "ageMin": "Age: {min}+", "ageMax": "Age: up to {max}", "create": {"title": "Create New Match", "description": "Set up a new archery match for participants to register", "basicInfo": "Basic Information", "divisions": "Divisions", "settings": "Settings", "form": {"name": "Match Name", "namePlaceholder": "Enter match name", "description": "Description", "descriptionPlaceholder": "Describe your match", "startDate": "Start Date", "endDate": "End Date", "country": "Country", "city": "City", "postcode": "Postcode", "address": "Address", "phone": "Phone", "email": "Email"}, "validation": {"nameRequired": "Match name is required", "descriptionRequired": "Match description is required", "startDateRequired": "Start date is required", "endDateRequired": "End date is required", "endDateAfterStart": "End date must be after start date", "startDateFuture": "Start date cannot be in the past", "divisionsRequired": "At least one division is required"}, "success": {"title": "Match Created Successfully", "message": "Your match has been created and is now available for registration.", "viewMatch": "View Match", "createAnother": "Create Another Match"}, "error": {"title": "Failed to Create Match", "message": "There was an error creating your match. Please try again.", "noOrganizer": "No organizer selected. Please select an organizer to create matches.", "noPrivileges": "You don't have organizer privileges to create matches."}, "draft": {"saved": "Draft saved", "loaded": "Draft loaded", "cleared": "Draft cleared"}}, "form": {"basicInfo": {"title": "Basic Information", "description": "Enter the basic details for your match"}, "name": {"label": "Match Name", "placeholder": "Enter match name"}, "description": {"label": "Description", "placeholder": "Describe your match"}, "startDate": {"label": "Start Date", "placeholder": "Select start date"}, "endDate": {"label": "End Date", "placeholder": "Select end date"}, "date": {"label": "Date", "placeholder": "Select date"}, "time": {"label": "Time", "placeholder": "Select time"}, "country": {"label": "Country", "placeholder": "Enter country"}, "city": {"label": "City", "placeholder": "Enter city"}, "postcode": {"label": "Postcode", "placeholder": "Enter postcode", "loading": "Looking up city..."}, "address": {"label": "Address", "placeholder": "Enter address"}, "phone": {"label": "Phone", "placeholder": "Enter phone number"}, "location": {"label": "Location", "clickToSelect": "Click on map to select location", "coordinates": "Coordinates", "selectedLocation": "Selected Location"}, "registrationEnds": {"label": "Registration Ends", "placeholder": "Select registration deadline"}, "matchType": {"label": "Match Type", "placeholder": "Select match type", "outdoor": "Outdoor", "indoor": "Indoor", "field": "Field", "3d": "3D"}, "competitionLevel": {"label": "Competition Level", "placeholder": "Select competition level", "local": "Local", "regional": "Regional", "national": "National", "international": "International"}, "maxPlayersAmount": {"label": "Maximum Players", "placeholder": "Enter maximum number of players"}, "international": {"label": "International Competition"}, "withoutLimits": {"label": "Without Limits"}, "publishAt": {"label": "Publish At", "placeholder": "Select when to publish"}, "currency": {"label": "<PERSON><PERSON><PERSON><PERSON>", "placeholder": "Select currency"}, "email": {"label": "Email", "placeholder": "Enter email address"}, "federation": {"label": "Federation", "placeholder": "Select federation"}, "latitude": {"label": "Latitude", "placeholder": "Enter latitude (e.g., 52.0693)"}, "longitude": {"label": "Longitude", "placeholder": "Enter longitude (e.g., 19.4803)"}, "details": {"title": "Match Details", "description": "Additional match information and settings"}, "divisions": {"title": "Divisions", "description": "Configure divisions for your match", "typeLabel": "Division Type", "federationDivisions": "Use Federation Divisions", "customDivisions": "Create Custom Divisions", "selectDivisionType": "Please select a division type to continue", "available": "Available Divisions", "selected": "Selected Divisions", "count": "{count} division(s) selected", "ageDivisions": "Age Divisions", "styleDivisions": "Style Divisions", "selectFederationFirst": "Please select a federation first to see available divisions", "customDivisionsComingSoon": "Custom divisions feature coming soon", "noDivisionsSelected": "No divisions selected yet", "addFirst": "Add First Division", "addCustom": "Add Custom Division", "name": "Division Name", "namePlaceholder": "Enter division name", "category": "Category", "selectCategory": "Select category", "categories": {"age": "Age Division", "style": "Style Division", "mixed": "Mixed Division"}, "equipmentType": "Equipment Type", "equipmentPlaceholder": "Enter equipment type", "ageMin": "Minimum Age", "ageMinPlaceholder": "Enter minimum age", "ageMax": "Maximum Age", "ageMaxPlaceholder": "Enter maximum age", "gender": "Gender", "genders": {"mixed": "Mixed", "male": "Male", "female": "Female"}, "maxParticipants": "Maximum Participants", "maxParticipantsPlaceholder": "Enter maximum participants", "registrationFee": "Registration Fee", "registrationFeePlaceholder": "Enter registration fee", "rules": "Rules", "rulesPlaceholder": "Enter division-specific rules"}, "settings": {"title": "Match Settings", "description": "Configure additional settings for your match", "allowLateRegistration": "Allow Late Registration", "requireLicense": "Require License", "allowEquipmentChange": "Allow Equipment Change", "publicResults": "Public Results", "emailNotifications": "Email Notifications"}, "actions": {"saveDraft": "Save Draft", "cancel": "Cancel", "create": "Create Match", "creating": "Creating..."}, "progress": {"step": "Step {current} of {total}"}, "validation": {"nameRequired": "Match name is required", "nameMinLength": "Match name must be at least 3 characters", "nameMaxLength": "Match name cannot exceed 100 characters", "descriptionRequired": "Match description is required", "descriptionMinLength": "Description must be at least 10 characters", "descriptionMaxLength": "Description cannot exceed 1000 characters", "startDateRequired": "Start date is required", "endDateRequired": "End date is required", "endDateAfterStart": "End date must be after start date", "startDatePast": "Start date cannot be in the past", "startDateFuture": "Start date must be at least 1 hour from now", "startDateBeforeEnd": "Start date must be before end date", "matchDurationTooLong": "Match duration cannot exceed 30 days", "minimumDuration": "Match must be at least 1 hour long", "emailInvalid": "Please enter a valid email address", "phoneInvalid": "Please enter a valid phone number", "divisionsRequired": "At least one division is required", "divisionTypeRequired": "Please select a division type", "federationRequired": "Federation is required", "divisionNameRequired": "Division name is required", "divisionCategoryRequired": "Division category is required", "divisionEquipmentRequired": "Equipment type is required", "divisionAgeRangeInvalid": "Minimum age must be less than maximum age", "divisionMaxParticipantsInvalid": "Maximum participants must be greater than 0", "divisionRegistrationFeeInvalid": "Registration fee cannot be negative", "duplicateDivisionNames": "Division names must be unique", "overlappingAgeRanges": "Age divisions cannot have overlapping age ranges for the same gender", "errorsFound": "Please fix the following errors:", "andMoreErrors": "...and {count} more error(s)"}, "summary": {"title": "Review Your Match", "description": "Review all the details before creating your match"}}}, "filters": {"toggleFilters": "Toggle filters", "thisWeek": "This week", "nextMonth": "Next month", "next3Months": "Next 3 months", "next6Months": "Next 6 months", "thisYear": "This year", "nearMe": "Near me", "available": "Available", "tournaments": "Tournaments", "active": "Active", "completed": "Completed"}, "calendar": {}, "tournament": {"detailsTitle": "Tournament Details", "loading": "Loading tournament details...", "error": "Error loading tournament details", "created": "Created", "completed": "Completed", "federation": "Federation", "organizer": "Organizer", "description": "Description", "upcomingMatches": "Upcoming Matches", "pastMatches": "Past Matches", "players": "Players", "playerId": "ID: {id}", "notFound": "Tournament not found", "selectToView": "Select a tournament to view details", "rounds": "Rounds", "round": "Round", "viewDetails": "View Details", "matches": "Matches", "noMatches": "No matches found", "totalMatches": "{count} total matches", "ongoing": "Ongoing", "ended": "Ended", "past": "Past", "future": "Future", "missing": "Missing", "registered": "Registered"}}