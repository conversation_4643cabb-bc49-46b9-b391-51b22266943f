<template>
  <div class="space-y-2">
    <Label v-if="label">{{ label }}</Label>
    <div class="relative">
      <div
        ref="mapContainer"
        class="w-full h-64 rounded-md border border-input bg-background"
        :class="{ 'border-destructive': error }"
        style="min-height: 256px;"
      />
      <div class="absolute top-2 right-2 z-[1000] bg-background/90 backdrop-blur-sm rounded-md p-2 text-xs text-muted-foreground">
        {{ t('matches.form.location.clickToSelect') }}
      </div>
    </div>
    <p v-if="error" class="text-sm text-destructive">{{ error }}</p>
    <div v-if="modelValue.latitude && modelValue.longitude" class="text-xs text-muted-foreground">
      {{ t('matches.form.location.coordinates') }}: {{ modelValue.latitude.toFixed(6) }}, {{ modelValue.longitude.toFixed(6) }}
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, watch, nextTick } from 'vue'
import { useI18n } from 'vue-i18n'
import L from 'leaflet'
import 'leaflet/dist/leaflet.css'
import { Label } from '@/components/ui/label'

interface LocationValue {
  latitude: number | undefined
  longitude: number | undefined
}

interface Props {
  modelValue: LocationValue
  label?: string
  error?: string
  placeholder?: string
  defaultCenter?: [number, number]
  defaultZoom?: number
}

interface Emits {
  (e: 'update:modelValue', value: LocationValue): void
}

const props = withDefaults(defineProps<Props>(), {
  placeholder: 'Click on the map to select location',
  defaultCenter: () => [52.0693, 19.4803], // Poland center
  defaultZoom: 6
})

const emit = defineEmits<Emits>()
const { t } = useI18n()

const mapContainer = ref<HTMLElement | null>(null)
let map: L.Map | null = null
let marker: L.Marker | null = null

// Create a custom marker icon
const createMarkerIcon = () => {
  const svgPinPath = "M168.3 499.2C116.1 435 0 279.4 0 192C0 85.96 85.96 0 192 0C298 0 384 85.96 384 192C384 279.4 267 435 215.7 499.2C203.4 514.5 180.6 514.5 168.3 499.2H168.3zM192 256C227.3 256 256 227.3 256 192C256 156.7 227.3 128 192 128C156.7 128 128 156.7 128 192C128 227.3 156.7 256 192 256z"

  const createColoredPinHtml = (color: string) => `
    <svg width="20" height="32" viewBox="0 0 384 512" xmlns="http://www.w3.org/2000/svg">
      <path fill="${color}" d="${svgPinPath}"/>
    </svg>
  `

  return L.divIcon({
    html: createColoredPinHtml('#ef4444'), // red color
    iconSize: [20, 32],
    iconAnchor: [10, 32],
    popupAnchor: [0, -32],
    className: 'leaflet-svg-icon'
  })
}

const initMap = () => {
  if (mapContainer.value && !map) {
    map = L.map(mapContainer.value, {
      zoomControl: true,
      attributionControl: false
    })

    L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
      maxZoom: 19,
      attribution: '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
    }).addTo(map)

    // Invalidate size to ensure proper rendering
    setTimeout(() => {
      if (map) {
        map.invalidateSize()
      }
    }, 100)

    // Set initial view
    if (props.modelValue.latitude && props.modelValue.longitude) {
      map.setView([props.modelValue.latitude, props.modelValue.longitude], 14)
      addMarker(props.modelValue.latitude, props.modelValue.longitude)
    } else {
      map.setView(props.defaultCenter, props.defaultZoom)
    }

    // Add click handler
    map.on('click', (e: L.LeafletMouseEvent) => {
      const { lat, lng } = e.latlng
      addMarker(lat, lng)
      emit('update:modelValue', { latitude: lat, longitude: lng })
    })
  }
}

const addMarker = (lat: number, lng: number) => {
  if (!map) return

  // Remove existing marker
  if (marker) {
    map.removeLayer(marker)
  }

  // Add new marker
  marker = L.marker([lat, lng], { icon: createMarkerIcon() })
    .addTo(map)
    .bindPopup(t('matches.form.location.selectedLocation'))
}

// Watch for external changes to modelValue
watch(() => props.modelValue, (newValue) => {
  if (!map) return

  if (newValue.latitude != null && newValue.longitude != null) {
    map.setView([newValue.latitude, newValue.longitude], 14)
    addMarker(newValue.latitude, newValue.longitude)
  } else if (marker) {
    map.removeLayer(marker)
    marker = null
  }
}, { deep: true })

const cleanupMap = () => {
  if (map) {
    map.remove()
    map = null
    marker = null
  }
}

const handleResize = () => {
  if (map) {
    map.invalidateSize()
  }
}

onMounted(() => {
  nextTick(() => {
    // Add a small delay to ensure the container is properly rendered
    setTimeout(() => {
      initMap()
    }, 100)
  })
  window.addEventListener('resize', handleResize)
})

onUnmounted(() => {
  cleanupMap()
  window.removeEventListener('resize', handleResize)
})
</script>

<style scoped>
:deep(.leaflet-control-container .leaflet-top,
      .leaflet-control-container .leaflet-bottom) {
  z-index: 20;
}

:deep(.leaflet-popup-content) {
  margin: 8px;
  font-size: 0.75rem;
}

:deep(.leaflet-popup-content-wrapper) {
  border-radius: 0.5rem;
}
</style>
