import { defineStore } from 'pinia'
import { computed, ref } from 'vue'

import type { Federation, FederationData, FederationQuery } from '../api/feathers-client'
import { api } from '../api/feathers-client'

// Division types based on actual database structure
export interface AgeDivision {
  name: string
  short_name: string
  min?: number
  max?: number
}

export interface StyleDivision {
  name: string
  short_name: string
  arrows: string[]
  bow_types: string[]
  equipments: string[]
  accessories: string[]
  bow_wood_types?: string[]
  shot_techniques: string[]
}

export interface Discipline {
  id: string
  name: string
  description?: string
  rules?: string
}

export const useFederationsStore = defineStore('federations', () => {
  // State
  const federations = ref<Federation[]>([])
  const isLoading = ref(false)
  const isInitialized = ref(false)
  const error = ref<Error | null>(null)

  // Use the typed service from the api client
  const federationsService = api.federations

  // Computed properties
  const activeFederations = computed(() =>
    federations.value.filter(federation => federation.isActive)
  )

  const federationsById = computed(() => {
    const map = new Map<number, Federation>()
    federations.value.forEach(federation => {
      map.set(federation.id, federation)
    })
    return map
  })

  // Actions
  async function fetchFederations(params: unknown = {}): Promise<Federation[]> {
    // If already cached and no specific params, return cached data
    if (isInitialized.value && Object.keys(params).length === 0) {
      return federations.value
    }

    isLoading.value = true
    error.value = null

    try {
      const result = await federationsService.find(params)
      const fetchedFederations = result.data || result

      // If this is a general fetch (no specific params), cache all federations
      if (Object.keys(params).length === 0) {
        federations.value = fetchedFederations
        isInitialized.value = true
      }

      return fetchedFederations
    } catch (err) {
      if (err instanceof Error) {
        error.value = err
      } else {
        error.value = new Error('Failed to fetch federations')
      }
      throw err
    } finally {
      isLoading.value = false
    }
  }

  async function getFederation(federationId: number): Promise<Federation> {
    // Check cache first
    const cached = federationsById.value.get(federationId)
    if (cached) {
      return cached
    }

    try {
      const federation = await federationsService.get(federationId)

      // Update cache
      const index = federations.value.findIndex(f => f.id === federationId)
      if (index >= 0) {
        federations.value[index] = federation
      } else {
        federations.value.push(federation)
      }

      return federation
    } catch (err) {
      if (err instanceof Error) {
        error.value = err
      } else {
        error.value = new Error('Failed to get federation')
      }
      throw err
    }
  }

  async function createFederation(federationData: FederationData): Promise<Federation> {
    try {
      const newFederation = await federationsService.create(federationData)

      // Add to cache
      federations.value.push(newFederation)

      return newFederation
    } catch (err) {
      if (err instanceof Error) {
        error.value = err
      } else {
        error.value = new Error('Failed to create federation')
      }
      throw err
    }
  }

  async function updateFederation(federationId: number, federationData: Partial<FederationData>): Promise<Federation> {
    try {
      const updatedFederation = await federationsService.patch(federationId, federationData)

      // Update cache
      const index = federations.value.findIndex(f => f.id === federationId)
      if (index >= 0) {
        federations.value[index] = updatedFederation
      }

      return updatedFederation
    } catch (err) {
      if (err instanceof Error) {
        error.value = err
      } else {
        error.value = new Error('Failed to update federation')
      }
      throw err
    }
  }

  async function deleteFederation(federationId: number): Promise<Federation> {
    try {
      const deletedFederation = await federationsService.remove(federationId)

      // Remove from cache
      const index = federations.value.findIndex(f => f.id === federationId)
      if (index >= 0) {
        federations.value.splice(index, 1)
      }

      return deletedFederation
    } catch (err) {
      if (err instanceof Error) {
        error.value = err
      } else {
        error.value = new Error('Failed to delete federation')
      }
      throw err
    }
  }

  // Helper methods for working with federation divisions
  function getFederationAgeDivisions(federationId: number): AgeDivision[] {
    const federation = federationsById.value.get(federationId)
    if (!federation || !federation.ageDivisions) {
      return []
    }

    try {
      // Parse JSON string if it's a string
      if (typeof federation.ageDivisions === 'string') {
        return JSON.parse(federation.ageDivisions) as AgeDivision[]
      }

      // Handle if it's already an array
      if (Array.isArray(federation.ageDivisions)) {
        return federation.ageDivisions as AgeDivision[]
      }

      // If it's an object, convert to array
      if (typeof federation.ageDivisions === 'object') {
        return Object.values(federation.ageDivisions) as AgeDivision[]
      }
    } catch (err) {
      console.warn('Failed to parse age divisions for federation', federationId, err)
    }

    return []
  }

  function getFederationStyleDivisions(federationId: number): StyleDivision[] {
    const federation = federationsById.value.get(federationId)
    if (!federation || !federation.styleDivisions) {
      return []
    }

    try {
      // Parse JSON string if it's a string
      if (typeof federation.styleDivisions === 'string') {
        return JSON.parse(federation.styleDivisions) as StyleDivision[]
      }

      // Handle if it's already an array
      if (Array.isArray(federation.styleDivisions)) {
        return federation.styleDivisions as StyleDivision[]
      }

      // If it's an object, convert to array
      if (typeof federation.styleDivisions === 'object') {
        return Object.values(federation.styleDivisions) as StyleDivision[]
      }
    } catch (err) {
      console.warn('Failed to parse style divisions for federation', federationId, err)
    }

    return []
  }

  function getFederationDisciplines(federationId: number): Discipline[] {
    const federation = federationsById.value.get(federationId)
    if (!federation || !federation.disciplines) {
      return []
    }

    // Handle different possible formats of disciplines
    if (Array.isArray(federation.disciplines)) {
      return federation.disciplines as Discipline[]
    }

    // If it's an object, convert to array
    if (typeof federation.disciplines === 'object') {
      return Object.values(federation.disciplines) as Discipline[]
    }

    return []
  }

  // Initialize federations on store creation
  async function initialize() {
    if (!isInitialized.value && !isLoading.value) {
      try {
        await fetchFederations()
      } catch (err) {
        console.warn('Failed to initialize federations store:', err)
      }
    }
  }

  // Error handling
  function clearError() {
    error.value = null
  }

  // Refresh cache
  async function refreshFederations(): Promise<Federation[]> {
    isInitialized.value = false
    return await fetchFederations()
  }

  // Initialize on store creation
  initialize()

  return {
    // State
    federations,
    isLoading,
    isInitialized,
    error,

    // Computed
    activeFederations,
    federationsById,

    // Actions
    fetchFederations,
    getFederation,
    createFederation,
    updateFederation,
    deleteFederation,
    refreshFederations,
    clearError,

    // Helper methods
    getFederationAgeDivisions,
    getFederationStyleDivisions,
    getFederationDisciplines
  }
})
