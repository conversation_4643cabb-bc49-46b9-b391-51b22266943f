import { describe, it, expect, vi, beforeEach } from 'vitest'
import { setActivePinia, createPinia } from 'pinia'
import { useFederationsStore } from '../federations'
import type { Federation } from '../../api/feathers-client'

// Mock the feathers client
vi.mock('../../api/feathers-client', () => ({
    api: {
        federations: {
            find: vi.fn(),
            get: vi.fn(),
            create: vi.fn(),
            patch: vi.fn(),
            remove: vi.fn()
        }
    }
}))

describe('Federations Store', () => {
    beforeEach(() => {
        setActivePinia(createPinia())
        vi.clearAllMocks()
    })

    it('should initialize with empty state', () => {
        const store = useFederationsStore()

        expect(store.federations).toEqual([])
        expect(store.isLoading).toBe(false)
        expect(store.isInitialized).toBe(false)
        expect(store.error).toBe(null)
    })

    it('should filter active federations', () => {
        const store = useFederationsStore()

        // Manually set federations for testing
        store.federations = [
            { id: 1, name: 'Active Fed', isActive: true },
            { id: 2, name: 'Inactive Fed', isActive: false },
            { id: 3, name: 'Another Active Fed', isActive: true }
        ] as unknown as Federation[]

        expect(store.activeFederations).toHaveLength(2)
        expect(store.activeFederations[0].name).toBe('Active Fed')
        expect(store.activeFederations[1].name).toBe('Another Active Fed')
    })

    it('should create federations by id map', () => {
        const store = useFederationsStore()

        const testFederations = [
            { id: 1, name: 'Fed 1', isActive: true },
            { id: 2, name: 'Fed 2', isActive: true }
        ] as unknown as Federation[]

        store.federations = testFederations

        expect(store.federationsById.get(1)).toEqual(testFederations[0])
        expect(store.federationsById.get(2)).toEqual(testFederations[1])
        expect(store.federationsById.get(3)).toBeUndefined()
    })

    it('should handle age divisions correctly', () => {
        const store = useFederationsStore()

        const testFederation = {
            id: 1,
            name: 'Test Fed',
            isActive: true,
            ageDivisions: JSON.stringify([
                { name: 'Youth', short_name: 'YU', min: 12, max: 17 },
                { name: 'Adult', short_name: 'AD', min: 18 }
            ])
        } as unknown as Federation

        store.federations = [testFederation]

        const ageDivisions = store.getFederationAgeDivisions(1)
        expect(ageDivisions).toHaveLength(2)
        expect(ageDivisions[0].name).toBe('Youth')
        expect(ageDivisions[1].name).toBe('Adult')
    })

    it('should handle style divisions correctly', () => {
        const store = useFederationsStore()

        const testFederation = {
            id: 1,
            name: 'Test Fed',
            isActive: true,
            styleDivisions: JSON.stringify([
                { name: 'Recurve', short_name: 'REC', arrows: ['carbon'], bow_types: ['recurve'], equipments: ['bow'], accessories: ['sight'], shot_techniques: ['mediterranean'] },
                { name: 'Compound', short_name: 'COM', arrows: ['carbon'], bow_types: ['compound'], equipments: ['bow'], accessories: ['sight'], shot_techniques: ['release'] }
            ])
        } as unknown as Federation

        store.federations = [testFederation]

        const styleDivisions = store.getFederationStyleDivisions(1)
        expect(styleDivisions).toHaveLength(2)
        expect(styleDivisions[0].name).toBe('Recurve')
        expect(styleDivisions[1].name).toBe('Compound')
    })

    it('should return empty arrays for non-existent federation', () => {
        const store = useFederationsStore()

        expect(store.getFederationAgeDivisions(999)).toEqual([])
        expect(store.getFederationStyleDivisions(999)).toEqual([])
        expect(store.getFederationDisciplines(999)).toEqual([])
    })
})
