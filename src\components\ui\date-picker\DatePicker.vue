<template>
  <Popover>
    <PopoverTrigger as-child>
      <Button
        variant="outline"
        :class="[
          'w-full justify-start text-left font-normal',
          !modelValue && 'text-muted-foreground',
          errorClass
        ]"
      >
        <CalendarIcon class="mr-2 h-4 w-4" />
        {{ modelValue ? formatDate(modelValue) : placeholder }}
      </Button>
    </PopoverTrigger>
    <PopoverContent class="w-auto p-0" align="start">
      <Calendar
        v-model="selectedDate"
        :disabled="disabled"
        :min-value="minDate"
        :max-value="maxDate"
        initial-focus
      />
    </PopoverContent>
  </Popover>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { CalendarDate, parseDate, today, getLocalTimeZone } from '@internationalized/date'
import { CalendarIcon } from 'lucide-vue-next'

import { Button } from '@/components/ui/button'
import { Calendar } from '@/components/ui/calendar'
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover'

interface Props {
  modelValue?: string
  placeholder?: string
  disabled?: boolean
  minDate?: CalendarDate
  maxDate?: CalendarDate
  error?: boolean
}

interface Emits {
  (e: 'update:modelValue', value: string): void
}

const props = withDefaults(defineProps<Props>(), {
  placeholder: 'Pick a date',
  disabled: false,
  error: false
})

const emit = defineEmits<Emits>()

const selectedDate = computed({
  get: () => {
    if (!props.modelValue) return undefined
    try {
      // Handle both date (YYYY-MM-DD) and datetime (YYYY-MM-DDTHH:MM) formats
      const dateOnly = props.modelValue.split('T')[0]
      return parseDate(dateOnly)
    } catch {
      return undefined
    }
  },
  set: (value: CalendarDate | undefined) => {
    if (value) {
      emit('update:modelValue', value.toString())
    } else {
      emit('update:modelValue', '')
    }
  }
})

const errorClass = computed(() => {
  return props.error ? 'border-destructive' : ''
})

function formatDate(dateString: string): string {
  try {
    // Handle both date (YYYY-MM-DD) and datetime (YYYY-MM-DDTHH:MM) formats
    const dateOnly = dateString.split('T')[0]
    const date = parseDate(dateOnly)
    return date.toDate(getLocalTimeZone()).toLocaleDateString()
  } catch {
    return dateString
  }
}
</script>
