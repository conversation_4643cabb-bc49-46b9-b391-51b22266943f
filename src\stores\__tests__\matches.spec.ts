import { describe, it, expect, beforeEach, vi, afterEach } from 'vitest'
import { setActivePinia, createP<PERSON> } from 'pinia'
import { useMatchesStore, type MatchFormData, type DivisionFormData, type MatchSettings } from '../matches'
import { useUserStore } from '../user'
import type { Match, MatchRegistration } from '../../api/feathers-client'

// Mock the API
vi.mock('../../api/feathers-client', () => ({
    app: {
        authenticate: vi.fn(),
        logout: vi.fn(),
        reAuthenticate: vi.fn()
    },
    api: {
        matches: {
            create: vi.fn()
        },
        matchRegistrations: {
            create: vi.fn()
        }
    }
}))

// Import the mocked API after mocking
import { api } from '../../api/feathers-client'

// Mock localStorage
const localStorageMock = {
    getItem: vi.fn(),
    setItem: vi.fn(),
    removeItem: vi.fn(),
    clear: vi.fn()
}

Object.defineProperty(window, 'localStorage', {
    value: localStorageMock
})

describe('Matches Store', () => {
    let matchesStore: ReturnType<typeof useMatchesStore>
    let userStore: ReturnType<typeof useUserStore>

    const mockOrganizer = {
        id: 1,
        name: 'Test Organizer',
        userId: 1,
        isActive: true
    }

    const mockDivision: DivisionFormData = {
        name: 'Test Division',
        category: 'style',
        equipmentType: 'recurve',
        gender: 'mixed',
        ageMin: 18,
        ageMax: 65,
        maxParticipants: 50,
        registrationFee: 25
    }

    const mockSettings: MatchSettings = {
        allowLateRegistration: true,
        requireLicense: false,
        allowEquipmentChange: true,
        publicResults: true,
        emailNotifications: true
    }

    const mockFormData: MatchFormData = {
        name: 'Test Match',
        description: 'Test match description',
        startDate: '2025-08-01',
        endDate: '2025-08-02',
        country: 'USA',
        city: 'Test City',
        address: '123 Test St',
        phone: '555-0123',
        email: '<EMAIL>',
        isActive: true,
        divisions: [mockDivision],
        settings: mockSettings
    }

    beforeEach(() => {
        setActivePinia(createPinia())
        matchesStore = useMatchesStore()
        userStore = useUserStore()

        // Reset all mocks
        vi.clearAllMocks()
        localStorageMock.getItem.mockReturnValue(null)
    })

    afterEach(() => {
        vi.clearAllMocks()
    })

    describe('Initial State', () => {
        it('should initialize with correct default state', () => {
            expect(matchesStore.isLoading).toBe(false)
            expect(matchesStore.isSubmitting).toBe(false)
            expect(matchesStore.error).toBeNull()
            expect(matchesStore.validationErrors).toEqual({})
            expect(matchesStore.hasValidationErrors).toBe(false)
        })

        it('should compute canCreateMatch based on active organizer and submission state', () => {
            // Test without organizer (default state)
            expect(matchesStore.canCreateMatch).toBe(false)

            // Test with submitting state
            matchesStore.isSubmitting = true
            expect(matchesStore.canCreateMatch).toBe(false)

            // Reset submitting state
            matchesStore.isSubmitting = false
            expect(matchesStore.canCreateMatch).toBe(false) // Still no organizer
        })
    })

    describe('Form Validation', () => {
        it('should validate required fields', () => {
            const invalidFormData: Partial<MatchFormData> = {
                name: '',
                description: '',
                divisions: []
            }

            const errors = matchesStore.validateMatchForm(invalidFormData as MatchFormData)

            expect(errors.name).toBe('Match name is required')
            expect(errors.description).toBe('Match description is required')
            expect(errors.startDate).toBe('Start date is required')
            expect(errors.endDate).toBe('End date is required')
            expect(errors.divisions).toBe('At least one division is required')
        })

        it('should validate date logic', () => {
            // Test end date before start date
            const invalidDates: MatchFormData = {
                ...mockFormData,
                startDate: '2025-08-02', // After end date
                endDate: '2025-08-01' // Before start date
            }

            const errors = matchesStore.validateMatchForm(invalidDates)
            expect(errors.endDate).toBe('End date must be after start date')

            // Test past date separately
            const yesterday = new Date()
            yesterday.setDate(yesterday.getDate() - 1)

            const pastDateForm: MatchFormData = {
                ...mockFormData,
                startDate: yesterday.toISOString().split('T')[0],
                endDate: '2025-08-02'
            }

            const pastErrors = matchesStore.validateMatchForm(pastDateForm)
            expect(pastErrors.startDate).toBe('Start date cannot be in the past')
        })

        it('should validate divisions', () => {
            const invalidDivisions: DivisionFormData[] = [
                {
                    name: '',
                    category: '',
                    equipmentType: '',
                    ageMin: 30,
                    ageMax: 20,
                    maxParticipants: -5,
                    registrationFee: -10
                }
            ]

            const formWithInvalidDivisions: MatchFormData = {
                ...mockFormData,
                divisions: invalidDivisions
            }

            const errors = matchesStore.validateMatchForm(formWithInvalidDivisions)

            expect(errors.divisions).toEqual([
                'Division 1: Name is required',
                'Division 1: Category is required',
                'Division 1: Equipment type is required',
                'Division 1: Minimum age must be less than maximum age',
                'Division 1: Maximum participants must be greater than 0',
                'Division 1: Registration fee cannot be negative'
            ])
        })

        it('should pass validation with valid data', () => {
            const errors = matchesStore.validateMatchForm(mockFormData)
            expect(Object.keys(errors)).toHaveLength(0)
        })
    })

    describe('Division Validation', () => {
        it('should validate individual division', () => {
            const invalidDivision: DivisionFormData = {
                name: '',
                category: '',
                equipmentType: '',
                ageMin: 25,
                ageMax: 20
            }

            const errors = matchesStore.validateDivision(invalidDivision)

            expect(errors).toContain('Division name is required')
            expect(errors).toContain('Division category is required')
            expect(errors).toContain('Equipment type is required')
            expect(errors).toContain('Minimum age must be less than maximum age')
        })

        it('should pass validation for valid division', () => {
            const errors = matchesStore.validateDivision(mockDivision)
            expect(errors).toHaveLength(0)
        })
    })

    describe('Match Creation', () => {
        beforeEach(() => {
            // Mock the activeOrganizer getter
            const mockActiveOrganizer = vi.fn().mockReturnValue(mockOrganizer)
            Object.defineProperty(userStore, 'activeOrganizer', {
                get: mockActiveOrganizer
            })
        })

        it('should create match successfully', async () => {
            const mockCreatedMatch: Match = {
                id: 1,
                name: 'Test Match',
                description: 'Test match description',
                organizerId: 1,
                isActive: true
            }

            vi.mocked(api.matches.create).mockResolvedValue(mockCreatedMatch)

            const result = await matchesStore.createMatch(mockFormData)

            expect(api.matches.create).toHaveBeenCalledWith({
                name: mockFormData.name,
                description: mockFormData.description,
                startDate: mockFormData.startDate,
                endDate: mockFormData.endDate,
                organizerId: mockOrganizer.id,
                styleDivisions: [mockDivision], // category === 'style'
                ageDivisions: [], // no age divisions in mock
                forWomen: false,
                forMen: false,
                maxPlayersAmount: 50,
                isActive: true,
                country: mockFormData.country,
                city: mockFormData.city,
                postcode: mockFormData.postcode,
                address: mockFormData.address,
                phone: mockFormData.phone,
                email: mockFormData.email
            })

            expect(result).toEqual(mockCreatedMatch)
            expect(matchesStore.isSubmitting).toBe(false)
            expect(matchesStore.error).toBeNull()
        })

        it('should handle validation errors during creation', async () => {
            const invalidFormData: MatchFormData = {
                ...mockFormData,
                name: '' // Invalid
            }

            await expect(matchesStore.createMatch(invalidFormData)).rejects.toThrow('Form validation failed')

            expect(matchesStore.validationErrors.name).toBe('Match name is required')
            expect(api.matches.create).not.toHaveBeenCalled()
        })

        it('should handle API errors during creation', async () => {
            const apiError = new Error('API Error')
            vi.mocked(api.matches.create).mockRejectedValue(apiError)

            await expect(matchesStore.createMatch(mockFormData)).rejects.toThrow('API Error')

            expect(matchesStore.error).toEqual(apiError)
            expect(matchesStore.isSubmitting).toBe(false)
        })

        it('should throw error when no active organizer', async () => {
            // Mock no organizer
            const mockActiveOrganizer = vi.fn().mockReturnValue(null)
            Object.defineProperty(userStore, 'activeOrganizer', {
                get: mockActiveOrganizer
            })

            await expect(matchesStore.createMatch(mockFormData)).rejects.toThrow('No active organizer selected')
        })
    })

    describe('Match Registration', () => {
        it('should register for match successfully', async () => {
            const mockRegistration: MatchRegistration = {
                id: 1,
                matchId: 1,
                playerId: 1,
                status: 'submitted'
            }

            const registrationData = {
                playerId: 1,
                styleDivision: 'recurve',
                status: 'submitted' as const
            }

            vi.mocked(api.matchRegistrations.create).mockResolvedValue(mockRegistration)

            const result = await matchesStore.registerForMatch(1, registrationData)

            expect(api.matchRegistrations.create).toHaveBeenCalledWith({
                ...registrationData,
                matchId: 1
            })
            expect(result).toEqual(mockRegistration)
        })

        it('should handle registration errors', async () => {
            const apiError = new Error('Registration failed')
            vi.mocked(api.matchRegistrations.create).mockRejectedValue(apiError)

            await expect(matchesStore.registerForMatch(1, { playerId: 1, status: 'submitted' })).rejects.toThrow('Registration failed')

            expect(matchesStore.error).toEqual(apiError)
        })
    })

    describe('Draft Management', () => {
        const mockDraftData: Partial<MatchFormData> = {
            name: 'Draft Match',
            description: 'Draft description'
        }

        it('should save draft to localStorage', () => {
            matchesStore.saveDraft(mockDraftData)

            expect(matchesStore.currentDraft?.formData).toEqual(mockDraftData)
            expect(localStorageMock.setItem).toHaveBeenCalledWith(
                'match-draft',
                expect.stringContaining('"formData"')
            )
        })

        it('should load draft from localStorage', () => {
            const mockStoredDraft = {
                formData: mockDraftData,
                lastSaved: '2025-07-29T12:00:00.000Z'
            }

            localStorageMock.getItem.mockReturnValue(JSON.stringify(mockStoredDraft))

            const loadedDraft = matchesStore.loadDraft()

            expect(loadedDraft?.formData).toEqual(mockDraftData)
            expect(loadedDraft?.lastSaved).toBeInstanceOf(Date)
            expect(matchesStore.currentDraft).toEqual(loadedDraft)
        })

        it('should handle localStorage errors gracefully', () => {
            localStorageMock.getItem.mockImplementation(() => {
                throw new Error('localStorage error')
            })

            const consoleSpy = vi.spyOn(console, 'warn').mockImplementation(() => { })

            const result = matchesStore.loadDraft()

            expect(result).toBeNull()
            expect(consoleSpy).toHaveBeenCalledWith('Failed to load draft from localStorage:', expect.any(Error))

            consoleSpy.mockRestore()
        })

        it('should clear draft', () => {
            matchesStore.currentDraft = {
                formData: mockDraftData,
                lastSaved: new Date()
            }

            matchesStore.clearDraft()

            expect(matchesStore.currentDraft).toBeNull()
            expect(localStorageMock.removeItem).toHaveBeenCalledWith('match-draft')
        })
    })

    describe('Error Handling', () => {
        it('should clear errors', () => {
            matchesStore.error = new Error('Test error')
            matchesStore.validationErrors = { name: 'Required' }

            matchesStore.clearErrors()

            expect(matchesStore.error).toBeNull()
            expect(matchesStore.validationErrors).toEqual({})
        })

        it('should set validation errors', () => {
            const errors = { name: 'Required', description: 'Too short' }

            matchesStore.setValidationErrors(errors)

            expect(matchesStore.validationErrors).toEqual(errors)
            expect(matchesStore.hasValidationErrors).toBe(true)
        })
    })

    describe('Computed Properties', () => {
        it('should compute hasValidationErrors correctly', () => {
            expect(matchesStore.hasValidationErrors).toBe(false)

            matchesStore.validationErrors = { name: 'Required' }
            expect(matchesStore.hasValidationErrors).toBe(true)

            matchesStore.validationErrors = {}
            expect(matchesStore.hasValidationErrors).toBe(false)
        })
    })
})
