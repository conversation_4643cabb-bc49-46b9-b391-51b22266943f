<template>
  <div class="space-y-8">
    <!-- Progress Indicator -->
    <div v-if="showProgress" class="w-full">
      <div class="flex items-center justify-between mb-2">
        <span class="text-sm font-medium text-muted-foreground">
          {{ t('matches.form.progress.step', { current: currentStep, total: totalSteps }) }}
        </span>
        <span class="text-sm text-muted-foreground">
          {{ Math.round((currentStep / totalSteps) * 100) }}%
        </span>
      </div>
      <div class="w-full bg-secondary rounded-full h-2">
        <div
          class="bg-primary h-2 rounded-full transition-all duration-300"
          :style="{ width: `${(currentStep / totalSteps) * 100}%` }"
        />
      </div>
    </div>

    <!-- Section Navigation -->
    <div v-if="showSectionNav" class="flex flex-wrap gap-2 border-b">
      <button
        v-for="(section, index) in sections"
        :key="section.id"
        type="button"
        :class="[
          'px-4 py-2 text-sm font-medium border-b-2 transition-colors',
          currentSection === section.id
            ? 'border-primary text-primary'
            : 'border-transparent text-muted-foreground hover:text-foreground hover:border-muted-foreground'
        ]"
        @click="$emit('section-change', section.id)"
      >
        {{ section.title }}
        <span v-if="section.hasErrors" class="ml-1 text-destructive">*</span>
      </button>
    </div>

    <!-- Section Content -->
    <div class="space-y-6">
      <slot />
    </div>

    <!-- Section Summary (for review) -->
    <div v-if="showSummary" class="space-y-4">
      <h3 class="text-lg font-semibold">{{ t('matches.form.summary.title') }}</h3>

      <div class="grid gap-4">
        <!-- Basic Info Summary -->
        <Card v-if="summaryData.basicInfo">
          <CardHeader class="pb-3">
            <CardTitle class="text-base">{{ t('matches.form.basicInfo.title') }}</CardTitle>
          </CardHeader>
          <CardContent class="space-y-2">
            <div class="grid grid-cols-2 gap-4 text-sm">
              <div>
                <span class="font-medium">{{ t('matches.form.name.label') }}:</span>
                {{ summaryData.basicInfo.name || t('common.na') }}
              </div>
              <div>
                <span class="font-medium">{{ t('matches.form.startDate.label') }}:</span>
                {{ formatDate(summaryData.basicInfo.startDate) || t('common.na') }}
              </div>
              <div>
                <span class="font-medium">{{ t('matches.form.endDate.label') }}:</span>
                {{ formatDate(summaryData.basicInfo.endDate) || t('common.na') }}
              </div>
              <div>
                <span class="font-medium">{{ t('matches.form.city.label') }}:</span>
                {{ summaryData.basicInfo.city || t('common.na') }}
              </div>
            </div>
          </CardContent>
        </Card>

        <!-- Divisions Summary -->
        <Card v-if="summaryData.divisions?.length">
          <CardHeader class="pb-3">
            <CardTitle class="text-base">
              {{ t('matches.form.divisions.title') }}
              <Badge variant="secondary" class="ml-2">
                {{ summaryData.divisions.length }}
              </Badge>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div class="space-y-2">
              <div
                v-for="(division, index) in summaryData.divisions"
                :key="index"
                class="flex items-center justify-between p-2 bg-muted rounded-md"
              >
                <div>
                  <span class="font-medium">{{ division.name }}</span>
                  <span class="text-sm text-muted-foreground ml-2">
                    ({{ division.category }})
                  </span>
                </div>
                <div class="text-sm text-muted-foreground">
                  {{ division.equipmentType }}
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        <!-- Settings Summary -->
        <Card v-if="summaryData.settings">
          <CardHeader class="pb-3">
            <CardTitle class="text-base">{{ t('matches.form.settings.title') }}</CardTitle>
          </CardHeader>
          <CardContent>
            <div class="grid grid-cols-2 gap-2 text-sm">
              <div class="flex items-center gap-2">
                <div :class="summaryData.settings.allowLateRegistration ? 'text-green-600' : 'text-muted-foreground'">
                  {{ summaryData.settings.allowLateRegistration ? '✓' : '✗' }}
                </div>
                {{ t('matches.form.settings.allowLateRegistration') }}
              </div>
              <div class="flex items-center gap-2">
                <div :class="summaryData.settings.requireLicense ? 'text-green-600' : 'text-muted-foreground'">
                  {{ summaryData.settings.requireLicense ? '✓' : '✗' }}
                </div>
                {{ t('matches.form.settings.requireLicense') }}
              </div>
              <div class="flex items-center gap-2">
                <div :class="summaryData.settings.allowEquipmentChange ? 'text-green-600' : 'text-muted-foreground'">
                  {{ summaryData.settings.allowEquipmentChange ? '✓' : '✗' }}
                </div>
                {{ t('matches.form.settings.allowEquipmentChange') }}
              </div>
              <div class="flex items-center gap-2">
                <div :class="summaryData.settings.publicResults ? 'text-green-600' : 'text-muted-foreground'">
                  {{ summaryData.settings.publicResults ? '✓' : '✗' }}
                </div>
                {{ t('matches.form.settings.publicResults') }}
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>

    <!-- Validation Summary -->
    <div v-if="validationErrors.length > 0" class="space-y-2">
      <h4 class="text-sm font-medium text-destructive">
        {{ t('matches.form.validation.errorsFound') }}
      </h4>
      <ul class="space-y-1">
        <li
          v-for="error in validationErrors"
          :key="error"
          class="text-sm text-destructive flex items-center gap-2"
        >
          <span class="w-1 h-1 bg-destructive rounded-full" />
          {{ error }}
        </li>
      </ul>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { useI18n } from 'vue-i18n'
import { format } from 'date-fns'

import { Badge } from '@/components/ui/badge'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'

import type { MatchFormData, DivisionFormData, MatchSettings } from '@/stores/matches'

// Types
interface FormSection {
  id: string
  title: string
  hasErrors: boolean
}

interface SummaryData {
  basicInfo?: {
    name: string
    startDate: string
    endDate: string
    city: string
  }
  divisions?: DivisionFormData[]
  settings?: MatchSettings
}

// Props and emits
interface Props {
  currentSection?: string
  currentStep?: number
  totalSteps?: number
  showProgress?: boolean
  showSectionNav?: boolean
  showSummary?: boolean
  sections?: FormSection[]
  summaryData?: SummaryData
  validationErrors?: string[]
}

interface Emits {
  (e: 'section-change', sectionId: string): void
}

const props = withDefaults(defineProps<Props>(), {
  currentSection: 'basic',
  currentStep: 1,
  totalSteps: 3,
  showProgress: false,
  showSectionNav: false,
  showSummary: false,
  sections: () => [],
  summaryData: () => ({}),
  validationErrors: () => []
})

const emit = defineEmits<Emits>()

// Composables
const { t } = useI18n()

// Helper functions
function formatDate(dateString: string): string {
  if (!dateString) return ''
  try {
    return format(new Date(dateString), 'PPP p')
  } catch {
    return dateString
  }
}
</script>
