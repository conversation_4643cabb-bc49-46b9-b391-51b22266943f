<template>
  <form @submit.prevent="handleSubmit" class="space-y-8">
    <MatchFormSections
      :current-section="currentSection"
      :current-step="currentStep"
      :total-steps="totalSteps"
      :show-progress="true"
      :show-section-nav="true"
      :sections="formSections"
      :validation-errors="allValidationErrors"
      @section-change="handleSectionChange"
    >
      <!-- Validation Summary -->
      <div v-if="allValidationErrors.length > 0" class="mb-6">
        <Alert variant="destructive">
          <AlertCircle class="h-4 w-4" />
          <AlertTitle>{{ t('matches.form.validation.errorsFound') }}</AlertTitle>
          <AlertDescription>
            <ul class="list-disc list-inside space-y-1 mt-2">
              <li v-for="error in allValidationErrors.slice(0, 5)" :key="error" class="text-sm">
                {{ error }}
              </li>
              <li v-if="allValidationErrors.length > 5" class="text-sm font-medium">
                {{ t('matches.form.validation.andMoreErrors', { count: allValidationErrors.length - 5 }) }}
              </li>
            </ul>
          </AlertDescription>
        </Alert>
      </div>

      <!-- Basic Information Section -->
      <Card v-show="currentSection === 'basic'">
        <CardHeader>
          <CardTitle>{{ t('matches.form.basicInfo.title') }}</CardTitle>
          <CardDescription>{{ t('matches.form.basicInfo.description') }}</CardDescription>
        </CardHeader>
        <CardContent class="space-y-6">
        <!-- Match Name -->
        <div class="space-y-2">
          <Label for="match-name">{{ t('matches.form.name.label') }}</Label>
          <Input
            id="match-name"
            v-model="formData.name"
            :placeholder="t('matches.form.name.placeholder')"
            :class="{ 'border-destructive': errors.name }"
            @blur="validateField('name')"
          />
          <p v-if="errors.name" class="text-sm text-destructive">{{ errors.name }}</p>
        </div>

        <!-- Match Description -->
        <div class="space-y-2">
          <Label for="match-description">{{ t('matches.form.description.label') }}</Label>
          <textarea
            id="match-description"
            v-model="formData.description"
            :placeholder="t('matches.form.description.placeholder')"
            :class="[
              'flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50',
              { 'border-destructive': errors.description }
            ]"
            rows="3"
            @blur="validateField('description')"
          />
          <p v-if="errors.description" class="text-sm text-destructive">{{ errors.description }}</p>
        </div>

        <!-- Date and Time Range -->
        <div class="space-y-4">
          <!-- Start Date and Time -->
          <div class="space-y-2">
            <Label>{{ t('matches.form.startDate.label') }}</Label>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-2">
              <div class="space-y-1">
                <Label for="start-date" class="text-sm text-muted-foreground">{{ t('matches.form.date.label') }}</Label>
                <DatePicker
                  id="start-date"
                  v-model="startDateOnly"
                  :placeholder="t('matches.form.date.placeholder')"
                  :error="!!errors.startDate"
                  @update:model-value="onStartDateChange"
                />
              </div>
              <div class="space-y-1">
                <Label for="start-time" class="text-sm text-muted-foreground">{{ t('matches.form.time.label') }}</Label>
                <Input
                  id="start-time"
                  v-model="startTimeOnly"
                  type="time"
                  :class="{ 'border-destructive': !!errors.startDate }"
                  @input="onStartTimeChange"
                />
              </div>
            </div>
            <p v-if="errors.startDate" class="text-sm text-destructive">{{ errors.startDate }}</p>
          </div>

          <!-- End Date and Time -->
          <div class="space-y-2">
            <Label>{{ t('matches.form.endDate.label') }}</Label>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-2">
              <div class="space-y-1">
                <Label for="end-date" class="text-sm text-muted-foreground">{{ t('matches.form.date.label') }}</Label>
                <DatePicker
                  id="end-date"
                  v-model="endDateOnly"
                  :placeholder="t('matches.form.date.placeholder')"
                  :error="!!errors.endDate"
                  :min-date="startDateOnly ? parseDate(startDateOnly) : undefined"
                  @update:model-value="onEndDateChange"
                />
              </div>
              <div class="space-y-1">
                <Label for="end-time" class="text-sm text-muted-foreground">{{ t('matches.form.time.label') }}</Label>
                <Input
                  id="end-time"
                  v-model="endTimeOnly"
                  type="time"
                  :class="{ 'border-destructive': !!errors.endDate }"
                  @input="onEndTimeChange"
                />
              </div>
            </div>
            <p v-if="errors.endDate" class="text-sm text-destructive">{{ errors.endDate }}</p>
          </div>
        </div>

        <!-- Location Information -->
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div class="space-y-2">
            <Label for="country">{{ t('matches.form.country.label') }}</Label>
            <Input
              id="country"
              v-model="formData.country"
              :placeholder="t('matches.form.country.placeholder')"
            />
          </div>

          <div class="space-y-2">
            <Label for="city">{{ t('matches.form.city.label') }}</Label>
            <Input
              id="city"
              v-model="formData.city"
              :placeholder="t('matches.form.city.placeholder')"
            />
          </div>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div class="space-y-2">
            <Label for="postcode">{{ t('matches.form.postcode.label') }}</Label>
            <Input
              id="postcode"
              v-model="formData.postcode"
              :placeholder="t('matches.form.postcode.placeholder')"
              @input="onPostcodeChange"
            />
            <p v-if="isLoadingPostcode" class="text-sm text-muted-foreground">
              {{ t('matches.form.postcode.loading') }}
            </p>
          </div>

          <div class="space-y-2">
            <Label for="address">{{ t('matches.form.address.label') }}</Label>
            <Input
              id="address"
              v-model="formData.address"
              :placeholder="t('matches.form.address.placeholder')"
            />
          </div>
        </div>

        <!-- Contact Information -->
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div class="space-y-2">
            <Label for="phone">{{ t('matches.form.phone.label') }}</Label>
            <Input
              id="phone"
              v-model="formData.phone"
              type="tel"
              :placeholder="t('matches.form.phone.placeholder')"
              :class="{ 'border-destructive': errors.phone }"
              @blur="validateField('phone')"
            />
            <p v-if="errors.phone" class="text-sm text-destructive">{{ errors.phone }}</p>
          </div>

          <div class="space-y-2">
            <Label for="email">{{ t('matches.form.email.label') }}</Label>
            <Input
              id="email"
              v-model="formData.email"
              type="email"
              :placeholder="t('matches.form.email.placeholder')"
              :class="{ 'border-destructive': errors.email }"
              @blur="validateField('email')"
            />
            <p v-if="errors.email" class="text-sm text-destructive">{{ errors.email }}</p>
          </div>

          <LocationPicker
            v-model="locationValue"
            :label="t('matches.form.location.label')"
            :error="errors.latitude || errors.longitude"
          />

          <!-- Manual Coordinates Input -->
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div class="space-y-2">
              <Label for="latitude">{{ t('matches.form.latitude.label') }}</Label>
              <Input
                id="latitude"
                v-model.number="formData.latitude"
                type="number"
                step="0.000001"
                :placeholder="t('matches.form.latitude.placeholder')"
                @input="validateField('latitude')"
                @blur="validateField('latitude')"
              />
              <p v-if="errors.latitude" class="text-sm text-destructive">{{ errors.latitude }}</p>
            </div>

            <div class="space-y-2">
              <Label for="longitude">{{ t('matches.form.longitude.label') }}</Label>
              <Input
                id="longitude"
                v-model.number="formData.longitude"
                type="number"
                step="0.000001"
                :placeholder="t('matches.form.longitude.placeholder')"
                @input="validateField('longitude')"
                @blur="validateField('longitude')"
              />
              <p v-if="errors.longitude" class="text-sm text-destructive">{{ errors.longitude }}</p>
            </div>
          </div>

          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div class="space-y-2">
              <Label for="registrationEnds">{{ t('matches.form.registrationEnds.label') }}</Label>
              <DatePicker
                id="registrationEnds"
                v-model="formData.registrationEnds"
                :placeholder="t('matches.form.registrationEnds.placeholder')"
                :error="!!errors.registrationEnds"
              />
              <p v-if="errors.registrationEnds" class="text-sm text-destructive">{{ errors.registrationEnds }}</p>
            </div>

          </div>
        </div>
      </CardContent>
    </Card>

    <!-- Federation and Divisions Section -->
    <Card v-show="currentSection === 'divisions'">
      <CardHeader>
        <CardTitle>{{ t('matches.form.divisions.title') }}</CardTitle>
        <CardDescription>{{ t('matches.form.divisions.description') }}</CardDescription>
      </CardHeader>
      <CardContent class="space-y-6">
        <!-- Division Type Selection -->
        <div class="space-y-4">
          <Label>{{ t('matches.form.divisions.typeLabel') }}</Label>
          <div class="flex gap-6">
            <div class="flex items-center space-x-2">
              <input
                id="federation-divisions"
                v-model="divisionType"
                type="radio"
                value="federation"
                class="h-4 w-4 text-primary border-gray-300 focus:ring-primary"
              />
              <Label for="federation-divisions" class="text-sm font-normal">
                {{ t('matches.form.divisions.federationDivisions') }}
              </Label>
            </div>
            <div class="flex items-center space-x-2">
              <input
                id="custom-divisions"
                v-model="divisionType"
                type="radio"
                value="custom"
                class="h-4 w-4 text-primary border-gray-300 focus:ring-primary"
              />
              <Label for="custom-divisions" class="text-sm font-normal">
                {{ t('matches.form.divisions.customDivisions') }}
              </Label>
            </div>
          </div>
        </div>

        <!-- Federation Selection (only when federation type is selected) -->
        <div v-if="divisionType === 'federation'" class="space-y-2">
          <Label for="federation">{{ t('matches.form.federation.label') }}</Label>
          <select
            id="federation"
            v-model="selectedFederationId"
            :class="[
              'flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50',
              { 'border-destructive': errors.federation }
            ]"
            @change="onFederationChange"
          >
            <option value="">{{ t('matches.form.federation.placeholder') }}</option>
            <option
              v-for="federation in activeFederations"
              :key="federation.id"
              :value="federation.id"
            >
              {{ federation.name }}
            </option>
          </select>
          <p v-if="errors.federation" class="text-sm text-destructive">{{ errors.federation }}</p>
        </div>

        <!-- Division Configuration -->
        <DivisionConfigForm
          v-if="divisionType === 'federation' && selectedFederationId"
          v-model:divisions="formData.divisions"
          :federation-id="selectedFederationId"
          :errors="divisionErrors"
          @validate="validateDivisions"
        />

        <!-- Custom Division Configuration (placeholder for now) -->
        <div v-if="divisionType === 'custom'" class="space-y-4">
          <div class="text-center py-8 text-muted-foreground">
            {{ t('matches.form.divisions.customDivisionsComingSoon') }}
          </div>
        </div>

        <div v-if="divisionType === 'federation' && !selectedFederationId" class="text-center py-8 text-muted-foreground">
          {{ t('matches.form.divisions.selectFederationFirst') }}
        </div>

        <div v-if="!divisionType" class="text-center py-8 text-muted-foreground">
          {{ t('matches.form.divisions.selectDivisionType') }}
        </div>
      </CardContent>
    </Card>

    <!-- Match Details Section -->
    <Card v-show="currentSection === 'details'">
      <CardHeader>
        <CardTitle>{{ t('matches.form.details.title') }}</CardTitle>
        <CardDescription>{{ t('matches.form.details.description') }}</CardDescription>
      </CardHeader>
      <CardContent class="space-y-6">
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
          <!-- Match Type -->
          <div class="space-y-2">
            <Label for="match-type">{{ t('matches.form.matchType.label') }}</Label>
            <Select v-model="formData.matchType">
              <SelectTrigger id="match-type">
                <SelectValue :placeholder="t('matches.form.matchType.placeholder')" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="outdoor">{{ t('matches.form.matchType.outdoor') }}</SelectItem>
                <SelectItem value="indoor">{{ t('matches.form.matchType.indoor') }}</SelectItem>
                <SelectItem value="field">{{ t('matches.form.matchType.field') }}</SelectItem>
                <SelectItem value="3d">{{ t('matches.form.matchType.3d') }}</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <!-- Competition Level -->
          <div class="space-y-2">
            <Label for="competition-level">{{ t('matches.form.competitionLevel.label') }}</Label>
            <Select v-model="formData.competitionLevel">
              <SelectTrigger id="competition-level">
                <SelectValue :placeholder="t('matches.form.competitionLevel.placeholder')" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="local">{{ t('matches.form.competitionLevel.local') }}</SelectItem>
                <SelectItem value="regional">{{ t('matches.form.competitionLevel.regional') }}</SelectItem>
                <SelectItem value="national">{{ t('matches.form.competitionLevel.national') }}</SelectItem>
                <SelectItem value="international">{{ t('matches.form.competitionLevel.international') }}</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <!-- Max Players -->
          <div class="space-y-2">
            <Label for="max-players">{{ t('matches.form.maxPlayersAmount.label') }}</Label>
            <Input
              id="max-players"
              v-model.number="formData.maxPlayersAmount"
              type="number"
              min="1"
              :placeholder="t('matches.form.maxPlayersAmount.placeholder')"
            />
          </div>

          <!-- Currency -->
          <div class="space-y-2">
            <Label for="currency">{{ t('matches.form.currency.label') }}</Label>
            <Select v-model="formData.currency">
              <SelectTrigger id="currency">
                <SelectValue :placeholder="t('matches.form.currency.placeholder')" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="PLN">PLN</SelectItem>
                <SelectItem value="EUR">EUR</SelectItem>
                <SelectItem value="USD">USD</SelectItem>
                <SelectItem value="CZK">CZK</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>

        <!-- Checkboxes -->
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div class="flex items-center space-x-2">
            <Switch
              id="international"
              v-model:checked="formData.international"
            />
            <Label for="international">{{ t('matches.form.international.label') }}</Label>
          </div>

          <div class="flex items-center space-x-2">
            <Switch
              id="without-limits"
              v-model:checked="formData.withoutLimits"
            />
            <Label for="without-limits">{{ t('matches.form.withoutLimits.label') }}</Label>
          </div>
        </div>

        <!-- Publish At -->
        <div class="space-y-2">
          <Label for="publish-at">{{ t('matches.form.publishAt.label') }}</Label>
          <DatePicker
            id="publish-at"
            v-model="formData.publishAt"
            :placeholder="t('matches.form.publishAt.placeholder')"
          />
        </div>
      </CardContent>
    </Card>

    <!-- Match Settings Section -->
    <Card v-show="currentSection === 'settings'">
      <CardHeader>
        <CardTitle>{{ t('matches.form.settings.title') }}</CardTitle>
        <CardDescription>{{ t('matches.form.settings.description') }}</CardDescription>
      </CardHeader>
      <CardContent class="space-y-4">
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div class="flex items-center space-x-2">
            <Switch
              id="allow-late-registration"
              v-model:checked="formData.settings.allowLateRegistration"
            />
            <Label for="allow-late-registration">{{ t('matches.form.settings.allowLateRegistration') }}</Label>
          </div>

          <div class="flex items-center space-x-2">
            <Switch
              id="require-license"
              v-model:checked="formData.settings.requireLicense"
            />
            <Label for="require-license">{{ t('matches.form.settings.requireLicense') }}</Label>
          </div>

          <div class="flex items-center space-x-2">
            <Switch
              id="allow-equipment-change"
              v-model:checked="formData.settings.allowEquipmentChange"
            />
            <Label for="allow-equipment-change">{{ t('matches.form.settings.allowEquipmentChange') }}</Label>
          </div>

          <div class="flex items-center space-x-2">
            <Switch
              id="public-results"
              v-model:checked="formData.settings.publicResults"
            />
            <Label for="public-results">{{ t('matches.form.settings.publicResults') }}</Label>
          </div>

          <div class="flex items-center space-x-2">
            <Switch
              id="email-notifications"
              v-model:checked="formData.settings.emailNotifications"
            />
            <Label for="email-notifications">{{ t('matches.form.settings.emailNotifications') }}</Label>
          </div>
        </div>
      </CardContent>
    </Card>

    <!-- Summary Section -->
    <Card v-show="currentSection === 'summary'">
      <CardHeader>
        <CardTitle>{{ t('matches.form.summary.title') }}</CardTitle>
        <CardDescription>{{ t('matches.form.summary.description') }}</CardDescription>
      </CardHeader>
      <CardContent class="space-y-6">
        <MatchFormSections
          :show-summary="true"
          :summary-data="summaryData"
        />
      </CardContent>
    </Card>

    <!-- Section Navigation -->
    <div class="flex flex-col sm:flex-row gap-4 justify-between items-center">
      <div class="flex gap-2">
        <Button
          v-if="currentSection !== 'basic'"
          type="button"
          variant="outline"
          @click="goToPreviousSection"
          :disabled="isSubmitting"
          class="flex items-center gap-2"
        >
          <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" />
          </svg>
          {{ t('common.previous') }}
        </Button>
      </div>

      <!-- Step Indicator -->
      <div class="flex items-center gap-2 text-sm text-muted-foreground">
        <span>{{ t('matches.form.progress.step', { current: currentStep, total: totalSteps }) }}</span>
      </div>

      <div class="flex gap-2">
        <Button
          type="button"
          variant="outline"
          @click="saveDraft"
          :disabled="isSubmitting"
          class="flex items-center gap-2"
        >
          <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7H5a2 2 0 00-2 2v9a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2h-3m-1 4l-3-3m0 0l-3 3m3-3v12" />
          </svg>
          {{ t('matches.form.actions.saveDraft') }}
        </Button>

        <Button
          type="button"
          variant="outline"
          @click="$emit('cancel')"
          :disabled="isSubmitting"
        >
          {{ t('matches.form.actions.cancel') }}
        </Button>

        <template v-if="currentSection !== 'summary'">
          <div class="relative">
            <Button
              type="button"
              @click="goToNextSection"
              :disabled="isSubmitting || !canProceedToNext"
              class="flex items-center gap-2"
              :title="!canProceedToNext ? getNextStepHint() : ''"
            >
              {{ t('common.next') }}
              <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
              </svg>
            </Button>

            <!-- Helper text for disabled next button -->
            <div v-if="!canProceedToNext && !isSubmitting" class="absolute top-full left-0 mt-1 text-xs text-muted-foreground max-w-48">
              {{ getNextStepHint() }}
            </div>
          </div>
        </template>

        <template v-else>
          <Button
            type="submit"
            :disabled="isSubmitting || !canSubmit"
            class="min-w-[120px] flex items-center gap-2"
          >
            <span v-if="isSubmitting" class="flex items-center gap-2">
              <div class="w-4 h-4 border-2 border-current border-t-transparent rounded-full animate-spin" />
              {{ t('matches.form.actions.creating') }}
            </span>
            <span v-else class="flex items-center gap-2">
              <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
              </svg>
              {{ t('matches.form.actions.create') }}
            </span>
          </Button>
        </template>
      </div>
    </div>
    </MatchFormSections>
  </form>
</template>

<script setup lang="ts">
import { computed, onMounted, ref, watch } from 'vue'
import { useI18n } from 'vue-i18n'
import { useDebounceFn } from '@vueuse/core'
import { parseDate } from '@internationalized/date'

import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Switch } from '@/components/ui/switch'
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert'
import { DatePicker } from '@/components/ui/date-picker'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select/index'
import { AlertCircle } from 'lucide-vue-next'

import DivisionConfigForm from './DivisionConfigForm.vue'
import MatchFormSections from './MatchFormSections.vue'
import LocationPicker from '@/components/map/LocationPicker.vue'

import { useMatchesStore, type MatchFormData, type MatchFormErrors } from '@/stores/matches'
import { useFederationsStore } from '@/stores/federations'
import { useUserStore } from '@/stores/user'
// import { getCountryName } from '../../utils/countries'

// Inline country conversion function
const COUNTRY_CODES: Record<string, string> = {
  'pl': 'Polska',
  'cz': 'Czechia',
  'sk': 'Slovakia',
  'hu': 'Hungary',
  'de': 'Germany',
  'at': 'Austria',
  'fr': 'France',
  'gb': 'United Kingdom',
  'us': 'United States',
  'ca': 'Canada',
  'au': 'Australia',
  'it': 'Italy',
  'es': 'Spain',
  'pt': 'Portugal',
  'nl': 'Netherlands',
  'be': 'Belgium',
  'ch': 'Switzerland',
  'se': 'Sweden',
  'no': 'Norway',
  'dk': 'Denmark',
  'fi': 'Finland',
  'ie': 'Ireland',
  'lu': 'Luxembourg',
  'si': 'Slovenia',
  'hr': 'Croatia',
  'rs': 'Serbia',
  'bg': 'Bulgaria',
  'ro': 'Romania',
  'gr': 'Greece',
  'cy': 'Cyprus',
  'mt': 'Malta',
  'ee': 'Estonia',
  'lv': 'Latvia',
  'lt': 'Lithuania'
}

function getCountryName(countryCode: string | undefined): string {
  if (!countryCode) return 'Polska' // Default to Poland

  const code = countryCode.toLowerCase()
  return COUNTRY_CODES[code] || countryCode
}

// Props and emits
interface Props {
  initialData?: Partial<MatchFormData>
  isLoading?: boolean
}

interface Emits {
  (e: 'submit', data: MatchFormData): void
  (e: 'cancel'): void
}

const props = withDefaults(defineProps<Props>(), {
  isLoading: false
})

const emit = defineEmits<Emits>()

// Composables
const { t } = useI18n()
const matchesStore = useMatchesStore()
const federationsStore = useFederationsStore()
const userStore = useUserStore()

// Reactive state
const selectedFederationId = ref<number | null>(null)
const divisionType = ref<'federation' | 'custom' | ''>('')
const errors = ref<MatchFormErrors>({})
const divisionErrors = ref<Record<string, string[]>>({})
const currentSection = ref<string>('basic')
const currentStep = ref<number>(1)
const isLoadingPostcode = ref<boolean>(false)

// Separate date and time handling
const startDateOnly = ref<string>('')
const startTimeOnly = ref<string>('08:00')
const endDateOnly = ref<string>('')
const endTimeOnly = ref<string>('18:00')

// Form data with defaults
const formData = ref<MatchFormData>({
  name: '',
  description: '',
  startDate: '',
  endDate: '',
  country: '',
  city: '',
  postcode: '',
  address: '',
  phone: '',
  email: '',
  latitude: undefined,
  longitude: undefined,
  registrationEnds: undefined,
  matchType: undefined,
  competitionLevel: undefined,
  maxPlayersAmount: undefined,
  international: false,
  withoutLimits: false,
  publishAt: undefined,
  currency: undefined,
  divisions: [],
  settings: {
    allowLateRegistration: false,
    requireLicense: true,
    allowEquipmentChange: false,
    publicResults: true,
    emailNotifications: true
  },
  isActive: true,
  ...props.initialData
})

// Computed properties
const activeFederations = computed(() => federationsStore.activeFederations)
const isSubmitting = computed(() => props.isLoading || matchesStore.isSubmitting)

const totalSteps = computed(() => 5)

const formSections = computed(() => [
  {
    id: 'basic',
    title: t('matches.form.basicInfo.title'),
    hasErrors: hasBasicInfoErrors.value
  },
  {
    id: 'divisions',
    title: t('matches.form.divisions.title'),
    hasErrors: hasDivisionErrors.value
  },
  {
    id: 'details',
    title: t('matches.form.details.title'),
    hasErrors: false
  },
  {
    id: 'settings',
    title: t('matches.form.settings.title'),
    hasErrors: false
  },
  {
    id: 'summary',
    title: t('matches.form.summary.title'),
    hasErrors: false
  }
])

const hasBasicInfoErrors = computed(() => {
  return !!(errors.value.name || errors.value.description || errors.value.startDate || errors.value.endDate)
})

const hasDivisionErrors = computed(() => {
  return !!(errors.value.federation || errors.value.divisions || Object.keys(divisionErrors.value).length > 0)
})

const allValidationErrors = computed(() => {
  const errorList: string[] = []

  // Basic info errors
  if (errors.value.name) errorList.push(errors.value.name as string)
  if (errors.value.description) errorList.push(errors.value.description as string)
  if (errors.value.startDate) errorList.push(errors.value.startDate as string)
  if (errors.value.endDate) errorList.push(errors.value.endDate as string)

  // Division errors
  if (errors.value.federation) errorList.push(errors.value.federation as string)
  if (errors.value.divisions) {
    if (Array.isArray(errors.value.divisions)) {
      errorList.push(...errors.value.divisions)
    } else {
      errorList.push(errors.value.divisions as string)
    }
  }

  // Individual division errors
  Object.values(divisionErrors.value).forEach(divErrors => {
    errorList.push(...divErrors)
  })

  return errorList
})

const canSubmit = computed(() => {
  return !isSubmitting.value &&
         userStore.activeOrganizer &&
         formData.value.name.trim() &&
         formData.value.description?.trim() &&
         formData.value.startDate &&
         formData.value.endDate &&
         formData.value.divisions.length > 0 &&
         Object.keys(errors.value).length === 0 &&
         Object.keys(divisionErrors.value).length === 0
})

const summaryData = computed(() => ({
  basicInfo: {
    name: formData.value.name,
    startDate: formData.value.startDate || '',
    endDate: formData.value.endDate || '',
    city: formData.value.city || '',
    country: formData.value.country || '',
    address: formData.value.address || '',
    phone: formData.value.phone || '',
    email: formData.value.email || ''
  },
  divisions: formData.value.divisions,
  settings: formData.value.settings
}))

const locationValue = computed({
  get: () => ({
    latitude: formData.value.latitude,
    longitude: formData.value.longitude
  }),
  set: (value: { latitude: number | undefined; longitude: number | undefined }) => {
    formData.value.latitude = value.latitude
    formData.value.longitude = value.longitude
  }
})

const canProceedToNext = computed(() => {
  switch (currentSection.value) {
    case 'basic':
      return formData.value.name.trim() &&
             formData.value.description?.trim() &&
             formData.value.startDate &&
             formData.value.endDate &&
             !hasBasicInfoErrors.value
    case 'divisions':
      return divisionType.value &&
             (divisionType.value === 'custom' || selectedFederationId.value) &&
             formData.value.divisions.length > 0 &&
             !hasDivisionErrors.value
    case 'settings':
      return true
    default:
      return true
  }
})

// Validation functions
function validateField(fieldName: keyof MatchFormData) {
  const fieldErrors: MatchFormErrors = {}

  switch (fieldName) {
    case 'name':
      if (!formData.value.name?.trim()) {
        fieldErrors.name = t('matches.form.validation.nameRequired')
      } else if (formData.value.name.trim().length < 3) {
        fieldErrors.name = t('matches.form.validation.nameMinLength')
      } else if (formData.value.name.trim().length > 100) {
        fieldErrors.name = t('matches.form.validation.nameMaxLength')
      }
      break
    case 'description':
      if (!formData.value.description?.trim()) {
        fieldErrors.description = t('matches.form.validation.descriptionRequired')
      } else if (formData.value.description.trim().length < 10) {
        fieldErrors.description = t('matches.form.validation.descriptionMinLength')
      } else if (formData.value.description.trim().length > 1000) {
        fieldErrors.description = t('matches.form.validation.descriptionMaxLength')
      }
      break
    case 'startDate':
      if (!formData.value.startDate) {
        fieldErrors.startDate = t('matches.form.validation.startDateRequired')
      } else {
        const startDate = new Date(formData.value.startDate)
        const now = new Date()
        const oneHourFromNow = new Date(now.getTime() + 60 * 60 * 1000)

        if (startDate < oneHourFromNow) {
          fieldErrors.startDate = t('matches.form.validation.startDateFuture')
        }

        // Cross-field validation with end date
        if (formData.value.endDate) {
          const endDate = new Date(formData.value.endDate)
          if (startDate >= endDate) {
            fieldErrors.startDate = t('matches.form.validation.startDateBeforeEnd')
          }

          // Check if the match duration is reasonable (not more than 30 days)
          const diffDays = (endDate.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24)
          if (diffDays > 30) {
            fieldErrors.startDate = t('matches.form.validation.matchDurationTooLong')
          }
        }
      }
      break
    case 'endDate':
      if (!formData.value.endDate) {
        fieldErrors.endDate = t('matches.form.validation.endDateRequired')
      } else if (formData.value.startDate && formData.value.endDate) {
        const startDate = new Date(formData.value.startDate)
        const endDate = new Date(formData.value.endDate)

        if (startDate >= endDate) {
          fieldErrors.endDate = t('matches.form.validation.endDateAfterStart')
        }

        // Check minimum duration (at least 1 hour)
        const diffHours = (endDate.getTime() - startDate.getTime()) / (1000 * 60 * 60)
        if (diffHours < 1) {
          fieldErrors.endDate = t('matches.form.validation.minimumDuration')
        }
      }
      break
    case 'email':
      if (formData.value.email && formData.value.email.trim()) {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
        if (!emailRegex.test(formData.value.email.trim())) {
          fieldErrors.email = t('matches.form.validation.emailInvalid')
        }
      }
      break
    case 'phone':
      if (formData.value.phone && formData.value.phone.trim()) {
        const phoneRegex = /^[\+]?[1-9][\d]{0,15}$/
        if (!phoneRegex.test(formData.value.phone.replace(/[\s\-\(\)]/g, ''))) {
          fieldErrors.phone = t('matches.form.validation.phoneInvalid')
        }
      }
      break
  }

  // Update errors
  if (Object.keys(fieldErrors).length > 0) {
    errors.value = { ...errors.value, ...fieldErrors }
  } else {
    // Remove error for this field
    const newErrors = { ...errors.value }
    delete newErrors[fieldName]
    errors.value = newErrors
  }
}

// Cross-field validation for divisions
function validateDivisionConflicts() {
  const divisionNames = formData.value.divisions.map(d => d.name.toLowerCase().trim())
  const duplicateNames = divisionNames.filter((name, index) =>
    name && divisionNames.indexOf(name) !== index
  )

  if (duplicateNames.length > 0) {
    errors.value = { ...errors.value, divisions: t('matches.form.validation.duplicateDivisionNames') }
    return false
  }

  // Check for conflicting age ranges in age divisions
  const ageDivisions = formData.value.divisions.filter(d => d.category === 'age')
  for (let i = 0; i < ageDivisions.length; i++) {
    for (let j = i + 1; j < ageDivisions.length; j++) {
      const div1 = ageDivisions[i]
      const div2 = ageDivisions[j]

      if (div1.gender === div2.gender || div1.gender === 'mixed' || div2.gender === 'mixed') {
        // Check for overlapping age ranges
        const min1 = div1.ageMin || 0
        const max1 = div1.ageMax || 100
        const min2 = div2.ageMin || 0
        const max2 = div2.ageMax || 100

        if ((min1 <= max2 && max1 >= min2)) {
          errors.value = { ...errors.value, divisions: t('matches.form.validation.overlappingAgeRanges') }
          return false
        }
      }
    }
  }

  return true
}

function validateDivisions(divErrors: Record<string, string[]>) {
  divisionErrors.value = divErrors

  if (formData.value.divisions.length === 0) {
    errors.value = { ...errors.value, divisions: t('matches.form.validation.divisionsRequired') }
  } else {
    // Clear division errors first
    const newErrors = { ...errors.value }
    delete newErrors.divisions
    errors.value = newErrors

    // Run cross-field validation for divisions
    validateDivisionConflicts()
  }
}

// Debounced validation for real-time feedback
const debouncedValidateField = useDebounceFn((fieldName: keyof MatchFormData) => {
  validateField(fieldName)
}, 300)

// Date and time handling
function combineDateTime(date: string, time: string): string {
  if (!date) return ''
  if (!time) return date + 'T00:00'
  return date + 'T' + time
}

function splitDateTime(dateTime: string): { date: string; time: string } {
  if (!dateTime) return { date: '', time: '' }
  const [date, time] = dateTime.split('T')
  return { date: date || '', time: time || '00:00' }
}

function onStartDateChange(newDate: string) {
  startDateOnly.value = newDate
  // Ensure we have a default time if none is set
  if (!startTimeOnly.value) {
    startTimeOnly.value = '08:00'
  }
  updateStartDateTime()
}

function onStartTimeChange(event: Event) {
  const target = event.target as HTMLInputElement
  startTimeOnly.value = target.value
  updateStartDateTime()
}

function updateStartDateTime() {
  const combined = combineDateTime(startDateOnly.value, startTimeOnly.value)
  formData.value.startDate = combined
  validateField('startDate')

  // Auto-set end date if not already set
  if (combined && !formData.value.endDate) {
    try {
      // Set end date to same day with default end time (18:00)
      endDateOnly.value = startDateOnly.value
      endTimeOnly.value = '18:00'
      formData.value.endDate = combineDateTime(endDateOnly.value, endTimeOnly.value)
    } catch (error) {
      console.warn('Failed to auto-set end date:', error)
    }
  }

  // Also revalidate end date when start date changes
  if (formData.value.endDate) {
    validateField('endDate')
  }
}

function onEndDateChange(newDate: string) {
  endDateOnly.value = newDate
  // Ensure we have a default time if none is set
  if (!endTimeOnly.value) {
    endTimeOnly.value = '18:00'
  }
  updateEndDateTime()
}

function onEndTimeChange(event: Event) {
  const target = event.target as HTMLInputElement
  endTimeOnly.value = target.value
  updateEndDateTime()
}

function updateEndDateTime() {
  const combined = combineDateTime(endDateOnly.value, endTimeOnly.value)
  formData.value.endDate = combined
  validateField('endDate')
}

// Postal code handling
const debouncedPostcodeLookup = useDebounceFn(async (postcode: string) => {
  if (!postcode || postcode.length < 5) return

  // Polish postal code format: XX-XXX
  const polishPostcodeRegex = /^\d{2}-?\d{3}$/
  if (!polishPostcodeRegex.test(postcode)) return

  // Normalize postcode format
  const normalizedPostcode = postcode.replace(/[^\d]/g, '').replace(/(\d{2})(\d{3})/, '$1-$2')

  isLoadingPostcode.value = true

  try {
    const response = await fetch(`https://kodpocztowy.intami.pl/api/${normalizedPostcode}`, {
      headers: {
        'Accept': 'application/json'
      }
    })

    if (response.ok) {
      const data = await response.json()
      console.log('Postcode API response:', data) // Debug log

      // Handle both single object and array responses
      let cityName = ''
      if (Array.isArray(data) && data.length > 0) {
        // API returns array - use first result
        cityName = data[0].miejscowosc || data[0].Miejscowość
      } else if (data && (data.miejscowosc || data.Miejscowość)) {
        // API returns single object
        cityName = data.miejscowosc || data.Miejscowość
      }

      if (cityName) {
        console.log('Setting city to:', cityName) // Debug log

        // Auto-fill city if not already set or if it's different
        if (!formData.value.city || formData.value.city !== cityName) {
          formData.value.city = cityName
        }

        // Auto-fill country if not already set (assuming Poland for Polish postal codes)
        if (!formData.value.country) {
          formData.value.country = 'Polska'
        }
      }
    }
  } catch (error) {
    console.warn('Failed to lookup postal code:', error)
  } finally {
    isLoadingPostcode.value = false
  }
}, 500)

function onPostcodeChange(event: Event) {
  const target = event.target as HTMLInputElement
  const postcode = target.value
  formData.value.postcode = postcode

  if (postcode && postcode.length >= 5) {
    debouncedPostcodeLookup(postcode)
  }
}

// Federation handling
function onFederationChange() {
  // Clear divisions when federation changes
  formData.value.divisions = []
  divisionErrors.value = {}

  // Clear federation error if one was selected
  if (selectedFederationId.value) {
    const newErrors = { ...errors.value }
    delete newErrors.federation
    errors.value = newErrors
  }
}

// Section management
function handleSectionChange(sectionId: string) {
  currentSection.value = sectionId

  // Update step based on section
  switch (sectionId) {
    case 'basic':
      currentStep.value = 1
      break
    case 'divisions':
      currentStep.value = 2
      break
    case 'details':
      currentStep.value = 3
      break
    case 'settings':
      currentStep.value = 4
      break
    case 'summary':
      currentStep.value = 5
      break
  }
}

function goToNextSection() {
  switch (currentSection.value) {
    case 'basic':
      if (canProceedToNext.value) {
        handleSectionChange('divisions')
      }
      break
    case 'divisions':
      if (canProceedToNext.value) {
        handleSectionChange('settings')
      }
      break
    case 'settings':
      if (canProceedToNext.value) {
        handleSectionChange('summary')
      }
      break
  }
}

function goToPreviousSection() {
  switch (currentSection.value) {
    case 'divisions':
      handleSectionChange('basic')
      break
    case 'settings':
      handleSectionChange('divisions')
      break
    case 'summary':
      handleSectionChange('settings')
      break
  }
}

function getNextStepHint(): string {
  switch (currentSection.value) {
    case 'basic':
      if (!formData.value.name.trim()) return t('matches.form.validation.nameRequired')
      if (!formData.value.description?.trim()) return t('matches.form.validation.descriptionRequired')
      if (!formData.value.startDate) return t('matches.form.validation.startDateRequired')
      if (!formData.value.endDate) return t('matches.form.validation.endDateRequired')
      if (hasBasicInfoErrors.value) return t('matches.form.validation.errorsFound')
      return ''
    case 'divisions':
      if (!divisionType.value) return t('matches.form.validation.divisionTypeRequired')
      if (divisionType.value === 'federation' && !selectedFederationId.value) return t('matches.form.validation.federationRequired')
      if (formData.value.divisions.length === 0) return t('matches.form.validation.divisionsRequired')
      if (hasDivisionErrors.value) return t('matches.form.validation.errorsFound')
      return ''
    default:
      return ''
  }
}

// Form submission
function handleSubmit() {
  // Validate all fields before submission
  validateField('name')
  validateField('description')
  validateField('startDate')
  validateField('endDate')

  // Only require federation selection if federation division type is selected
  if (divisionType.value === 'federation' && !selectedFederationId.value) {
    errors.value.federation = t('matches.form.validation.federationRequired')
  }

  if (formData.value.divisions.length === 0) {
    errors.value.divisions = t('matches.form.validation.divisionsRequired')
  }

  // Check if there are any validation errors
  if (Object.keys(errors.value).length > 0 || Object.keys(divisionErrors.value).length > 0) {
    return
  }

  emit('submit', formData.value)
}

// Draft management
function saveDraft() {
  matchesStore.saveDraft(formData.value)
  // Could show a toast notification here
}

// Watch for real-time validation
watch(() => formData.value.name, () => debouncedValidateField('name'))
watch(() => formData.value.description, () => debouncedValidateField('description'))
watch(() => formData.value.endDate, () => {
  debouncedValidateField('endDate')
  // Also revalidate start date when end date changes
  if (formData.value.startDate) {
    debouncedValidateField('startDate')
  }
})
watch(() => formData.value.email, () => debouncedValidateField('email'))
watch(() => formData.value.phone, () => debouncedValidateField('phone'))

// Debounced geocoding function
const debouncedGeocodeAddress = useDebounceFn(async () => {
  const { address, city, country, postcode } = formData.value
  // Only geocode if we have at least city or postcode
  if ((city && country) || postcode) {
    try {
      // Build query with available address components
      const queryParts = []
      if (address) queryParts.push(address)
      if (postcode) queryParts.push(postcode)
      if (city) queryParts.push(city)
      if (country) queryParts.push(country)

      const query = queryParts.join(', ')
      const response = await fetch(`https://nominatim.openstreetmap.org/search?format=json&q=${encodeURIComponent(query)}&limit=1&countrycodes=pl`)
      const data = await response.json()

      if (data && data.length > 0) {
        const result = data[0]
        formData.value.latitude = parseFloat(result.lat)
        formData.value.longitude = parseFloat(result.lon)
      }
    } catch (error) {
      console.warn('Failed to geocode address:', error)
    }
  }
}, 1000)

// Watch for address changes to update map location
watch([
  () => formData.value.address,
  () => formData.value.city,
  () => formData.value.country,
  () => formData.value.postcode
], () => {
  debouncedGeocodeAddress()
})

// Watch manual coordinate changes to update locationValue
watch([
  () => formData.value.latitude,
  () => formData.value.longitude
], () => {
  locationValue.value = {
    latitude: formData.value.latitude,
    longitude: formData.value.longitude
  }
})

// Watch divisions for conflict validation
watch(() => formData.value.divisions, () => {
  if (formData.value.divisions.length > 0) {
    validateDivisionConflicts()
  }
}, { deep: true })

// Initialize separate date/time values from formData
watch(() => formData.value.startDate, (newStartDate) => {
  if (newStartDate) {
    const { date, time } = splitDateTime(newStartDate)
    startDateOnly.value = date
    startTimeOnly.value = time || '08:00' // Use default if no time
  }
}, { immediate: true })

watch(() => formData.value.endDate, (newEndDate) => {
  if (newEndDate) {
    const { date, time } = splitDateTime(newEndDate)
    endDateOnly.value = date
    endTimeOnly.value = time || '18:00' // Use default if no time
  }
}, { immediate: true })

// Watch division type changes to clear divisions and federation
watch(divisionType, (newType, oldType) => {
  if (oldType && newType !== oldType) {
    // Clear divisions when switching types
    formData.value.divisions = []
    // Clear federation selection when switching to custom
    if (newType === 'custom') {
      selectedFederationId.value = null
    }
  }
})

// Initialize component
onMounted(async () => {
  // Load federations if not already loaded
  if (!federationsStore.isInitialized) {
    try {
      await federationsStore.fetchFederations()
    } catch (err) {
      console.error('Failed to load federations:', err)
    }
  }

  // Auto-populate country from active organizer if not already set
  if (!formData.value.country && !props.initialData?.country) {
    const organizer = userStore.activeOrganizer
    if (organizer?.country) {
      // Convert country code to country name
      formData.value.country = getCountryName(organizer.country)
    } else {
      // Default to Poland if no organizer country
      formData.value.country = 'Polska'
    }
  }

  // Load draft if available
  const draft = matchesStore.loadDraft()
  if (draft && !props.initialData) {
    formData.value = { ...formData.value, ...draft.formData }
    if (draft.formData.divisions?.length) {
      // Try to determine federation from divisions if not explicitly set
      // This would need federation context in divisions or separate storage
    }
  }
})
</script>
